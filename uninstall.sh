#!/bin/bash

[[ -s "$HOME/.bin" ]] || ln -s "$root/bin" "$HOME/.bin"

for file in $(ls config); do
  target="$HOME/.config/$file"

  if [ -L $target ]; then
    echo "Removing symlink $target"
    rm $target
  fi
done

for file in $(ls home); do
  target="$HOME/.$file"

  if [ -L $target ]; then
    echo "Removing symlink $target"
    rm $target
  fi
done

if [[ $(os) == "MacOS" ]]; then
  for file in $(ls config); do
    target="$HOME/.config/$file"

    if [[ -s $target ]] || [[ -d $target ]]; then
      echo "Removing $target"
      rm -r $target
    fi
  done

  for file in $(ls vscode); do
    target="$HOME/Library/Application Support/Code/User/$file"

    if [[ -s $target ]] || [[ -d $target ]]; then
      echo "Removing $target"
      rm -r $target
    fi
  done
fi
