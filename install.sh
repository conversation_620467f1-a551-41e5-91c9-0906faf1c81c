#!/bin/bash

root="$(pwd)"

# Function to copy files from source to target
copy_file() {
  local source=$1
  local target=$2

  echo "Copying $source => $target"
  if [ -d "$source" ]; then
    mkdir -p "$target"
    cp -Rf "$source/"* "$target/"
  else
    cp -Rf "$source" "$target"
  fi
}

[[ -s "$HOME/.bin" ]] || ln -s "$root/bin" "$HOME/.bin"

export PATH="$root/bin:$PATH"

# Install Oh My Zsh if not already installed
if [ ! -d "$HOME/.oh-my-zsh" ]; then
  echo "Installing Oh My Zsh..."
  sh -c "$(curl -fsSL https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh)" "" --unattended
fi

for file in $(ls home | grep -v "gitconfig"); do
  source="$root/home/<USER>"
  target="$HOME/.$file"
  copy_file "$source" "$target"
done

if [[ $(os) == "MacOS" ]]; then
  for file in $(ls home | grep "gitconfig"); do
    source="$root/home/<USER>"
    target="$HOME/.$file"
    copy_file "$source" "$target"
  done
fi

# MacOS only
if [[ $(os) == "MacOS" ]]; then
  for file in $(ls config); do
    source="$root/config/$file"
    target="$HOME/.config/$file"
    copy_file "$source" "$target"
  done

  for file in $(ls vscode); do
    source="$root/vscode/$file"
    target="$HOME/Library/Application Support/Cursor/User/$file"
    copy_file "$source" "$target"
  done

  echo "Allow press-and-hold key repeat for Visual Studio Code"
  defaults write com.microsoft.VSCodeInsiders ApplePressAndHoldEnabled -bool false
  defaults write com.microsoft.VSCode ApplePressAndHoldEnabled -bool false
  defaults write com.getcursor.Cursor ApplePressAndHoldEnabled -bool false

  echo "Setting up MacGPG2 for git"
  git config --global gpg.program /usr/local/MacGPG2/bin/gpg2
fi

# Copy custom Oh My Zsh themes
if [ -d "$HOME/.oh-my-zsh" ] && [ -d "$root/home/<USER>/custom/themes" ]; then
  echo "Copying custom Oh My Zsh themes"
  mkdir -p "$HOME/.oh-my-zsh/custom/themes"
  cp -f "$root/home/<USER>/custom/themes/"*.zsh-theme "$HOME/.oh-my-zsh/custom/themes/"
fi

echo "Installing default gems"
# for gem in $(cat $HOME/.default-gems); do gem i $gem --conservative; done
