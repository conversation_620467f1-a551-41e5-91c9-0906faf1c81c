{"profiles": [{"complex_modifications": {"rules": [{"description": "Post escape if capslock is pressed alone. Post left_control if pressed and hold.", "manipulators": [{"from": {"key_code": "caps_lock", "modifiers": {"optional": ["any"]}}, "to": [{"key_code": "left_control"}], "to_if_alone": [{"key_code": "escape"}], "type": "basic"}]}]}, "devices": [{"identifiers": {"is_keyboard": true, "product_id": 1957, "vendor_id": 1118}, "ignore": true, "manipulate_caps_lock_led": false}, {"identifiers": {"is_keyboard": true, "product_id": 24868, "vendor_id": 7504}, "ignore": true}], "fn_function_keys": [{"from": {"key_code": "f3"}, "to": [{"key_code": "mission_control"}]}, {"from": {"key_code": "f4"}, "to": [{"key_code": "launchpad"}]}, {"from": {"key_code": "f5"}, "to": [{"key_code": "illumination_decrement"}]}, {"from": {"key_code": "f6"}, "to": [{"key_code": "illumination_increment"}]}, {"from": {"key_code": "f9"}, "to": [{"consumer_key_code": "fastforward"}]}], "name": "Default profile", "selected": true, "virtual_hid_keyboard": {"keyboard_type_v2": "iso"}}]}