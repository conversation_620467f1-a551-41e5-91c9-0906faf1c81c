/**
 * LLM API Library
 *
 * This library provides a unified interface for interacting with different LLM APIs:
 * - Anthropic (Claude)
 * - OpenAI
 * - Local LLM Studio (OpenAI-compatible)
 *
 * Usage:
 * const llm = require('./lib/llm');
 *
 * // Initialize with a provider
 * const model = llm.create({
 *   provider: 'anthropic', // or 'openai' or 'local'
 *   apiKey: process.env.ANTHROPIC_API_KEY,
 *   model: 'claude-3-7-sonnet-20250219'
 * });
 *
 * // Generate text
 * const response = await model.generateText({
 *   prompt: 'Hello, world!',
 *   maxTokens: 1000,
 *   temperature: 0.7
 * });
 */

const AnthropicProvider = require('./providers/anthropic');
const OpenAIProvider = require('./providers/openai');
const LocalProvider = require('./providers/local');

/**
 * Create an LLM provider instance
 *
 * @param {Object} config - Configuration object
 * @param {string} config.provider - Provider name ('anthropic', 'openai', or 'local')
 * @param {string} config.apiKey - API key for the provider
 * @param {string} config.model - Model name to use
 * @param {string} [config.baseUrl] - Base URL for API (used for local provider)
 * @returns {Object} LLM provider instance
 */
function create(config) {
  switch (config.provider.toLowerCase()) {
    case 'anthropic':
      return new AnthropicProvider(config);
    case 'openai':
      return new OpenAIProvider(config);
    case 'local':
      return new LocalProvider(config);
    default:
      throw new Error(`Unsupported LLM provider: ${config.provider}`);
  }
}

/**
 * Get provider configuration from environment variables
 *
 * @returns {Object} Provider configuration
 */
function getConfigFromEnv() {
  // Default to Anthropic if no provider specified
  const provider = process.env.LLM_PROVIDER || 'anthropic';

  let config = {
    provider,
  };

  switch (provider.toLowerCase()) {
    case 'anthropic':
      config.apiKey = process.env.ANTHROPIC_API_KEY || process.env.CLAUDE_API_KEY;
      config.model = process.env.ANTHROPIC_MODEL || 'claude-3-7-sonnet-20250219';
      break;
    case 'openai':
      config.apiKey = process.env.OPENAI_API_KEY;
      config.model = process.env.OPENAI_MODEL || 'gpt-4o';
      break;
    case 'local':
      config.apiKey = process.env.LOCAL_LLM_API_KEY || '';
      config.baseUrl = process.env.LOCAL_LLM_BASE_URL || 'http://localhost:1234/v1';
      config.model = process.env.LOCAL_LLM_MODEL || 'local-model';
      break;
  }

  return config;
}

module.exports = {
  create,
  getConfigFromEnv
};
