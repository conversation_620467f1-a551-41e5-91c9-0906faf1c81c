/**
 * OpenAI Provider
 * 
 * Implementation of the LLM interface for OpenAI's API
 */

const axios = require('axios');

class OpenAIProvider {
  /**
   * Create a new OpenAI provider
   * 
   * @param {Object} config - Configuration object
   * @param {string} config.apiKey - OpenAI API key
   * @param {string} config.model - Model name (e.g., 'gpt-4o')
   * @param {string} [config.baseUrl] - Base URL for API (optional)
   */
  constructor(config) {
    this.apiKey = config.apiKey;
    this.model = config.model;
    this.baseUrl = config.baseUrl || 'https://api.openai.com/v1';
    this.apiUrl = `${this.baseUrl}/chat/completions`;
  }

  /**
   * Generate text using the OpenAI API
   * 
   * @param {Object} params - Generation parameters
   * @param {string} params.prompt - The prompt to send to the model
   * @param {number} [params.maxTokens=1000] - Maximum number of tokens to generate
   * @param {number} [params.temperature=0.7] - Temperature for generation
   * @param {string} [params.systemPrompt] - System prompt to use
   * @returns {Promise<string>} Generated text
   */
  async generateText(params) {
    try {
      const { prompt, maxTokens = 1000, temperature = 0.7, systemPrompt } = params;

      const messages = [];
      
      // Add system prompt if provided
      if (systemPrompt) {
        messages.push({
          role: 'system',
          content: systemPrompt
        });
      }
      
      // Add user prompt
      messages.push({
        role: 'user',
        content: prompt
      });

      const response = await axios.post(
        this.apiUrl,
        {
          model: this.model,
          messages: messages,
          max_tokens: maxTokens,
          temperature: temperature
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          }
        }
      );

      return response.data.choices[0].message.content;
    } catch (error) {
      console.error('Error generating text with OpenAI:');
      if (error.response) {
        console.error('API response:', error.response.data);
      } else {
        console.error(error.message);
      }
      throw error;
    }
  }
}

module.exports = OpenAIProvider;
