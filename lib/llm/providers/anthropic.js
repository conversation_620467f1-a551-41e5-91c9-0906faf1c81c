/**
 * Anthropic (<PERSON>) Provider
 * 
 * Implementation of the LLM interface for Anthropic's Claude API
 */

const axios = require('axios');

class AnthropicProvider {
  /**
   * Create a new Anthropic provider
   * 
   * @param {Object} config - Configuration object
   * @param {string} config.apiKey - Anthropic API key
   * @param {string} config.model - Model name (e.g., 'claude-3-7-sonnet-20250219')
   */
  constructor(config) {
    this.apiKey = config.apiKey;
    this.model = config.model;
    this.apiUrl = 'https://api.anthropic.com/v1/messages';
    this.apiVersion = '2023-06-01';
  }

  /**
   * Generate text using the Anthropic Claude API
   * 
   * @param {Object} params - Generation parameters
   * @param {string} params.prompt - The prompt to send to the model
   * @param {number} [params.maxTokens=1000] - Maximum number of tokens to generate
   * @param {number} [params.temperature=0.7] - Temperature for generation
   * @param {Array} [params.systemPrompt] - System prompt to use
   * @returns {Promise<string>} Generated text
   */
  async generateText(params) {
    try {
      const { prompt, maxTokens = 1000, temperature = 0.7, systemPrompt } = params;

      const requestBody = {
        model: this.model,
        max_tokens: maxTokens,
        temperature: temperature,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      };

      // Add system prompt if provided
      if (systemPrompt) {
        requestBody.system = systemPrompt;
      }

      const response = await axios.post(
        this.apiUrl,
        requestBody,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': this.apiKey,
            'anthropic-version': this.apiVersion
          }
        }
      );

      return response.data.content[0].text;
    } catch (error) {
      console.error('Error generating text with Anthropic:');
      if (error.response) {
        console.error('API response:', error.response.data);
      } else {
        console.error(error.message);
      }
      throw error;
    }
  }
}

module.exports = AnthropicProvider;
