/**
 * Local LLM Provider
 *
 * Implementation of the LLM interface for local LLM Studio or other OpenAI-compatible APIs
 */

const axios = require('axios');

class LocalProvider {
  /**
   * Create a new Local LLM provider
   *
   * @param {Object} config - Configuration object
   * @param {string} [config.apiKey] - API key (may be empty for local deployments)
   * @param {string} config.model - Model name
   * @param {string} [config.baseUrl] - Base URL for API (default: http://localhost:1234/v1)
   */
  constructor(config) {
    this.apiKey = config.apiKey || '';
    this.model = config.model || 'local-model';
    this.baseUrl = config.baseUrl || 'http://localhost:1234/v1';
    this.apiUrl = `${this.baseUrl}/chat/completions`;

    console.log(`Local LLM configured with:
    - Base URL: ${this.baseUrl}
    - Model: ${this.model}
    - API URL: ${this.apiUrl}`);
  }

  /**
   * Generate text using the local LLM API
   *
   * @param {Object} params - Generation parameters
   * @param {string} params.prompt - The prompt to send to the model
   * @param {number} [params.maxTokens=1000] - Maximum number of tokens to generate
   * @param {number} [params.temperature=0.7] - Temperature for generation
   * @param {string} [params.systemPrompt] - System prompt to use
   * @returns {Promise<string>} Generated text
   */
  async generateText(params) {
    try {
      const { prompt, maxTokens = 1000, temperature = 0.7, systemPrompt } = params;

      const messages = [];

      // Add system prompt if provided
      if (systemPrompt) {
        messages.push({
          role: 'system',
          content: systemPrompt
        });
      }

      // Add user prompt
      messages.push({
        role: 'user',
        content: prompt
      });

      const headers = {
        'Content-Type': 'application/json'
      };

      // Add API key if provided
      if (this.apiKey) {
        headers['Authorization'] = `Bearer ${this.apiKey}`;
      }

      console.log(`Sending request to LM Studio at ${this.apiUrl}`);

      // First, try to get available models to verify connection
      try {
        const modelsResponse = await axios.get(
          `${this.baseUrl}/models`,
          { headers }
        );
        console.log(`Available models: ${JSON.stringify(modelsResponse.data.data.map(m => m.id))}`);

        // If we got models, use the first one if our model isn't specified
        if (this.model === 'local-model' && modelsResponse.data.data.length > 0) {
          this.model = modelsResponse.data.data[0].id;
          console.log(`Using first available model: ${this.model}`);
        }
      } catch (modelError) {
        console.log(`Could not fetch models: ${modelError.message}`);
        // Continue with the request anyway
      }

      const requestBody = {
        model: this.model,
        messages: messages,
        max_tokens: maxTokens,
        temperature: temperature
      };

      console.log(`Request payload: ${JSON.stringify(requestBody, null, 2)}`);

      const response = await axios.post(
        this.apiUrl,
        requestBody,
        { headers }
      );

      console.log(`Response received from LM Studio`);

      if (!response.data.choices || !response.data.choices[0]) {
        console.error('Unexpected response format:', response.data);
        throw new Error('Unexpected response format from LM Studio');
      }

      return response.data.choices[0].message.content;
    } catch (error) {
      console.error('Error generating text with Local LLM:');
      if (error.response) {
        console.error('API response status:', error.response.status);
        console.error('API response data:', error.response.data);
      } else {
        console.error('Error message:', error.message);
        if (error.request) {
          console.error('No response received from server');
        }
      }
      throw error;
    }
  }
}

module.exports = LocalProvider;
