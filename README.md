# Dynamic GIT_EDITOR Configuration

This setup allows you to automatically switch between different editors (VS Code and Cursor) based on the project you're working on, with smart fallbacks for different environments.

## How it works

The `GIT_EDITOR` environment variable is automatically updated whenever you change directories. The editor is determined by:

1. First checking if you're in a devcontainer/remote environment (always uses VS Code in this case)
2. Then checking for a `.editor-config` file in the current directory
3. If no config file exists, it auto-detects based on project files:
   - Elixir projects (with `mix.exs`) use Cursor (if available)
   - Ruby projects (with `Gemfile`) use VS Code
   - Default is VS Code
4. Verifies the selected editor is actually installed and available
5. Falls back to alternatives (code → vim → nano) if the selected editor isn't available

## Setting up a project-specific editor

Create a `.editor-config` file in your project root with just the name of the editor:

```
# For VS Code
code

# OR for Cursor
cursor
```

## Devcontainer Support

When running in a devcontainer or GitHub Codespace, the script automatically detects this environment and uses VS Code, which is properly configured for remote editing.

## Customizing detection rules

You can modify the detection logic in `home/zsh/code.zsh` by editing the `get_git_editor()` function.
