{"version": "2.0.0", "options": {"shell": {"executable": "zsh", "args": ["-l", "-i", "-c"]}, "cwd": "${workspaceFolder}/${relativeFileDirname}/../.."}, "tasks": [{"label": "check env", "type": "shell", "command": "env", "problemMatcher": []}, {"label": "check ruby version", "type": "shell", "command": "echo `ruby -v`", "problemMatcher": []}, {"label": "bundle", "type": "shell", "command": "bundle", "problemMatcher": []}, {"label": "test all", "type": "shell", "command": "source ~/.zshrc && t", "problemMatcher": [], "group": "test", "presentation": {"reveal": "always", "focus": true}}, {"label": "test current file", "type": "shell", "command": "source ~/.zshrc && t ${relativeFile}", "problemMatcher": [], "group": "test", "presentation": {"reveal": "always", "focus": true}}, {"label": "test current line", "type": "shell", "command": "source ~/.zshrc && t ${relativeFile}:${lineNumber}", "presentation": {"reveal": "always", "focus": true}}, {"label": "rubocop format", "type": "shell", "command": "rubocop -a ${relativeFile}", "problemMatcher": [], "group": "build", "presentation": {"reveal": "always", "focus": true}}]}