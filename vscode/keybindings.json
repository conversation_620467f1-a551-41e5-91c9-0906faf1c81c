[
  // UI
  {
    "key": "ctrl+j",
    "command": "workbench.action.focusPanel",
    "when": "!panelFocus"
  },
  {
    "key": "ctrl+j",
    "command": "workbench.action.closePanel",
    "when": "panelFocus"
  },
  {
    "key": "ctrl+p g",
    "command": "workbench.view.scm"
  },
  {
    "key": "ctrl+p d",
    "command": "workbench.debug.action.toggleRepl"
  },
  {
    "key": "ctrl+p s",
    "command": "workbench.view.search"
  },
  {
    "key": "ctrl+p t",
    "command": "workbench.action.terminal.focus"
  },
  {
    "key": "ctrl+p o",
    "command": "workbench.action.output.toggleOutput"
  },
  {
    "key": "ctrl+p p",
    "command": "workbench.actions.view.toggleProblems"
  },
  // Find/Replace
  {
    "key": "cmd+r",
    "command": "editor.action.startFindReplaceAction",
    "when": "editorFocus"
  },
  {
    "key": "ctrl+c",
    "command": "toggleSearchEditorCaseSensitive",
    "when": "findWidgetVisible"
  },
  {
    "key": "ctrl+w",
    "command": "toggleSearchEditorWholeWord",
    "when": "findWidgetVisible"
  },
  // Panel
  {
    "key": "cmd+h",
    "command": "workbench.action.previousPanelView",
    "when": "!editorFocus"
  },
  {
    "key": "cmd+l",
    "command": "workbench.action.nextPanelView",
    "when": "!editorFocus"
  },
  // Terminal
  {
    "key": "ctrl+]",
    "command": "workbench.action.terminal.focusNext"
  },
  {
    "key": "ctrl+]",
    "command": "workbench.action.terminal.focusNext",
    "when": "!editorTextFocus"
  },
  {
    "key": "ctrl+[",
    "command": "workbench.action.terminal.focusPrevious",
    "when": "!editorTextFocus"
  },
  // Editor
  {
    "key": "ctrl+w s",
    "command": "reset",
    "when": "editorTextFocus"
  },
  {
    "key": "cmd+h",
    "command": "workbench.action.previousEditor",
    "when": "editorTextFocus"
  },
  {
    "key": "cmd+l",
    "command": "workbench.action.nextEditor",
    "when": "editorTextFocus"
  },
  {
    "key": "ctrl+h",
    "command": "workbench.action.previousEditor",
    "when": "editorTextFocus"
  },
  {
    "key": "ctrl+l",
    "command": "workbench.action.nextEditor",
    "when": "editorTextFocus"
  },
  {
    "key": "ctrl+e l",
    "command": "workbench.action.moveEditorRightInGroup",
    "when": "editorTextFocus"
  },
  {
    "key": "ctrl+e h",
    "command": "workbench.action.moveEditorLeftInGroup",
    "when": "editorTextFocus"
  },
  // Ruby
  {
    "key": "ctrl+t b",
    "command": "workbench.action.tasks.runTask",
    "args": "bundle"
  },
  {
    "key": "ctrl+t l",
    "command": "workbench.action.tasks.runTask",
    "args": "test current line"
  },
  {
    "key": "ctrl+t d",
    "command": "workbench.action.tasks.runTask",
    "args": "debug current line"
  },
  {
    "key": "ctrl+t f",
    "command": "workbench.action.tasks.runTask",
    "args": "test current file"
  },
  {
    "key": "ctrl+t a",
    "command": "workbench.action.tasks.runTask",
    "args": "test all"
  },
  // Git
  {
    "key": "ctrl+c s",
    "command": "git.commitStaged",
    "when": "!terminalFocus"
  },
  {
    "key": "ctrl+c a",
    "command": "git.commitAll",
    "when": "!terminalFocus"
  },
  {
    "key": "ctrl+g r",
    "command": "git.revertSelectedRanges",
    "when": "!terminalFocus"
  },
  {
    "key": "ctrl+g l",
    "command": "gitlens.showCommitsView",
    "when": "!terminalFocus"
  },
  {
    "key": "ctrl+g c",
    "command": "git.checkout",
    "when": "!terminalFocus"
  },
  {
    "key": "ctrl+g p",
    "command": "git.pullRebase",
    "when": "!terminalFocus"
  },
  {
    "key": "ctrl+g s",
    "command": "git.syncRebase",
    "when": "!terminalFocus"
  },
  {
    "key": "ctrl+s l",
    "command": "git.stageSelectedRanges",
    "when": "!terminalFocus"
  },
  {
    "key": "ctrl+m s",
    "command": "magit.status",
    "when": "!terminalFocus"
  },
  {
    "key": "ctrl+m c",
    "command": "magit.commit",
    "when": "!terminalFocus"
  },
  {
    "key": "ctrl+m b",
    "command": "magit.branching",
    "when": "!terminalFocus"
  },
  {
    "key": "ctrl+m f",
    "command": "magit.file-popup",
    "when": "!terminalFocus"
  },
  {
    "key": "ctrl+m d",
    "command": "magit.dispatch",
    "when": "!terminalFocus"
  },
  {
    "key": "ctrl+s f",
    "command": "gitlens.views.stageFile",
    "when": "!terminalFocus"
  },
  // Debug
  {
    "key": "ctrl+d s",
    "command": "workbench.action.debug.start",
    "when": "editorTextFocus"
  },
  {
    "key": "ctrl+j",
    "command": "workbench.action.debug.stepOver",
    "when": "inDebugRepl"
  },
  {
    "key": "ctrl+i",
    "command": "workbench.action.debug.stepInto",
    "when": "inDebugRepl"
  },
  {
    "key": "ctrl+o",
    "command": "workbench.action.debug.stepOut",
    "when": "inDebugRepl"
  },
  {
    "key": "ctrl+s",
    "command": "workbench.action.debug.stop",
    "when": "inDebugRepl"
  },
  {
    "key": "ctrl+b",
    "command": "editor.debug.action.toggleBreakpoint",
    "when": "editorTextFocus"
  },
  {
    "key": "cmd+t",
    "command": "workbench.action.terminal.new",
    "when": "terminalFocus"
  },
  {
    "key": "ctrl+w j",
    "command": "workbench.action.focusPanel"
  },
  {
    "key": "ctrl+w h",
    "command": "workbench.action.focusPreviousGroup"
  },
  {
    "key": "ctrl+w l",
    "command": "workbench.action.focusNextGroup"
  },
  {
    "key": "ctrl+w v",
    "command": "workbench.action.splitEditor"
  },
  {
    "key": "ctrl+w m",
    "command": "workbench.action.maximizeEditor"
  },
  {
    "key": "ctrl+w r",
    "command": "workbench.action.evenEditorWidths"
  },
  {
    "key": "cmd+w",
    "command": "workbench.action.terminal.kill",
    "when": "terminalFocus"
  },
  {
    "key": "ctrl+L",
    "command": "workbench.action.terminal.clear",
    "when": "terminalFocus"
  },
  {
    "key": "ctrl+o r",
    "command": "workbench.action.openRecent"
  },
  {
    "key": "ctrl+r",
    "command": "-workbench.action.openRecent"
  },
  {
    "key": "ctrl+cmd+k",
    "command": "workbench.action.toggleZenMode"
  },
  {
    "key": "cmd+k z",
    "command": "-workbench.action.toggleZenMode"
  },
  {
    "key": "ctrl+cmd+l",
    "command": "workbench.action.nextPanelView",
    "when": "!editorTextFocus"
  },
  {
    "key": "ctrl+cmd+h",
    "command": "workbench.action.previousPanelView",
    "when": "!editorTextFocus"
  },
  {
    "key": "ctrl+.",
    "command": "toggleexcludedfiles.toggle"
  },
  {
    "key": "shift+cmd+a",
    "command": "-toggleexcludedfiles.toggle",
    "when": "filesExplorerFocus"
  },
  {
    "key": "alt+b",
    "command": "-gitlens.toggleFileBlame",
    "when": "editorTextFocus && config.gitlens.keymap == 'alternate' && gitlens:activeFileStatus =~ /blameable/"
  },
  {
    "key": "ctrl+g b",
    "command": "gitlens.toggleFileBlame",
    "when": "editorTextFocus && config.gitlens.keymap == 'chorded' && gitlens:activeFileStatus =~ /blameable/"
  },
  {
    "key": "alt+cmd+g b",
    "command": "-gitlens.toggleFileBlame",
    "when": "editorTextFocus && config.gitlens.keymap == 'chorded' && gitlens:activeFileStatus =~ /blameable/"
  },
  {
    "key": "ctrl+f d",
    "command": "fileutils.duplicateFile"
  },
  {
    "key": "ctrl+f r",
    "command": "fileutils.renameFile"
  },
  {
    "key": "ctrl+k",
    "command": "magit.discard-at-point",
    "when": "editorTextFocus && editorLangId == 'magit' && vim.mode =~ /^(?!SearchInProgressMode|CommandlineInProgress).*$/"
  },
  {
    "key": "k",
    "command": "-magit.discard-at-point",
    "when": "editorTextFocus && editorLangId == 'magit' && vim.mode =~ /^(?!SearchInProgressMode|CommandlineInProgress).*$/"
  },
  {
    "key": "ctrl+cmd+w",
    "command": "-workbench.action.toggleTabsVisibility"
  },
  {
    "key": "ctrl+shift+m",
    "command": "-editor.action.toggleTabFocusMode"
  },
  {
    "key": "ctrl+m p",
    "command": "magit.pushing"
  },
  {
    "key": "shift+p",
    "command": "-magit.pushing",
    "when": "editorTextFocus && editorLangId == 'magit' && vim.mode =~ /^(?!SearchInProgressMode|CommandlineInProgress).*$/"
  },
  {
    "key": "ctrl+cmd+l",
    "command": "workbench.action.moveEditorToNextGroup",
    "when": "editorFocus"
  },
  {
    "key": "ctrl+cmd+right",
    "command": "-workbench.action.moveEditorToNextGroup"
  },
  {
    "key": "ctrl+cmd+h",
    "command": "workbench.action.moveEditorToPreviousGroup",
    "when": "editorFocus"
  },
  {
    "key": "ctrl+cmd+left",
    "command": "-workbench.action.moveEditorToPreviousGroup"
  },
  // Docker
  {
    "key": "ctrl+d u",
    "command": "vscode-docker.compose.up"
  },
  {
    "key": "ctrl+d s",
    "command": "vscode-docker.containers.composeGroup.stop"
  },
  {
    "key": "ctrl+f f",
    "command": "editor.action.formatDocument",
    "when": "editorHasDocumentFormattingProvider && editorTextFocus && !editorReadonly && !inCompositeEditor"
  },
  {
    "key": "shift+alt+f",
    "command": "-editor.action.formatDocument",
    "when": "editorHasDocumentFormattingProvider && editorTextFocus && !editorReadonly && !inCompositeEditor"
  },
  {
    "key": "ctrl+d h",
    "command": "extension.dash.specific",
    "when": "editorTextFocus"
  },
  {
    "key": "ctrl+h",
    "command": "-extension.dash.specific",
    "when": "editorTextFocus"
  },
  {
    "key": "ctrl+s c",
    "command": "search.action.clearSearchResults",
    "when": "panelFocus"
  }
]
