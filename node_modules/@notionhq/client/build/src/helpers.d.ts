import { BlockObjectResponse, CommentObjectResponse, DatabaseObjectResponse, EquationRichTextItemResponse, PageObjectResponse, PartialBlockObjectResponse, PartialCommentObjectResponse, PartialDatabaseObjectResponse, PartialPageObjectResponse, PartialUserObjectResponse, RichTextItemResponse, TextRichTextItemResponse, UserObjectResponse } from "./api-endpoints";
interface PaginatedArgs {
    start_cursor?: string;
}
interface PaginatedList<T> {
    object: "list";
    results: T[];
    next_cursor: string | null;
    has_more: boolean;
}
/**
 * Returns an async iterator over the results of any paginated Notion API.
 *
 * Example (given a notion Client called `notion`):
 *
 * ```
 * for await (const block of iteratePaginatedAPI(notion.blocks.children.list, {
 *   block_id: parentBlockId,
 * })) {
 *   // Do something with block.
 * }
 * ```
 *
 * @param listFn A bound function on the Notion client that represents a conforming paginated
 *   API. Example: `notion.blocks.children.list`.
 * @param firstPageArgs Arguments that should be passed to the API on the first and subsequent
 *   calls to the API. Any necessary `next_cursor` will be automatically populated by
 *   this function. Example: `{ block_id: "<my block id>" }`
 */
export declare function iteratePaginatedAPI<Args extends PaginatedArgs, Item>(listFn: (args: Args) => Promise<PaginatedList<Item>>, firstPageArgs: Args): AsyncIterableIterator<Item>;
/**
 * Collect all of the results of paginating an API into an in-memory array.
 *
 * Example (given a notion Client called `notion`):
 *
 * ```
 * const blocks = await collectPaginatedAPI(notion.blocks.children.list, {
 *   block_id: parentBlockId,
 * })
 * // Do something with blocks.
 * ```
 *
 * @param listFn A bound function on the Notion client that represents a conforming paginated
 *   API. Example: `notion.blocks.children.list`.
 * @param firstPageArgs Arguments that should be passed to the API on the first and subsequent
 *   calls to the API. Any necessary `next_cursor` will be automatically populated by
 *   this function. Example: `{ block_id: "<my block id>" }`
 */
export declare function collectPaginatedAPI<Args extends PaginatedArgs, Item>(listFn: (args: Args) => Promise<PaginatedList<Item>>, firstPageArgs: Args): Promise<Item[]>;
/**
 * @returns `true` if `response` is a full `BlockObjectResponse`.
 */
export declare function isFullBlock(response: PageObjectResponse | PartialPageObjectResponse | DatabaseObjectResponse | PartialDatabaseObjectResponse | BlockObjectResponse | PartialBlockObjectResponse): response is BlockObjectResponse;
/**
 * @returns `true` if `response` is a full `PageObjectResponse`.
 */
export declare function isFullPage(response: PageObjectResponse | PartialPageObjectResponse | DatabaseObjectResponse | PartialDatabaseObjectResponse | BlockObjectResponse | PartialBlockObjectResponse): response is PageObjectResponse;
/**
 * @returns `true` if `response` is a full `DatabaseObjectResponse`.
 */
export declare function isFullDatabase(response: PageObjectResponse | PartialPageObjectResponse | DatabaseObjectResponse | PartialDatabaseObjectResponse | BlockObjectResponse | PartialBlockObjectResponse): response is DatabaseObjectResponse;
/**
 * @returns `true` if `response` is a full `DatabaseObjectResponse` or a full
 * `PageObjectResponse`.
 */
export declare function isFullPageOrDatabase(response: PageObjectResponse | PartialPageObjectResponse | DatabaseObjectResponse | PartialDatabaseObjectResponse | BlockObjectResponse | PartialBlockObjectResponse): response is DatabaseObjectResponse | PageObjectResponse;
/**
 * @returns `true` if `response` is a full `UserObjectResponse`.
 */
export declare function isFullUser(response: UserObjectResponse | PartialUserObjectResponse): response is UserObjectResponse;
/**
 * @returns `true` if `response` is a full `CommentObjectResponse`.
 */
export declare function isFullComment(response: CommentObjectResponse | PartialCommentObjectResponse): response is CommentObjectResponse;
/**
 * @returns `true` if `richText` is a `TextRichTextItemResponse`.
 */
export declare function isTextRichTextItemResponse(richText: RichTextItemResponse): richText is TextRichTextItemResponse;
/**
 * @returns `true` if `richText` is an `EquationRichTextItemResponse`.
 */
export declare function isEquationRichTextItemResponse(richText: RichTextItemResponse): richText is EquationRichTextItemResponse;
/**
 * @returns `true` if `richText` is an `MentionRichTextItemResponse`.
 */
export declare function isMentionRichTextItemResponse(richText: RichTextItemResponse): richText is EquationRichTextItemResponse;
export {};
//# sourceMappingURL=helpers.d.ts.map