{"version": 3, "file": "logging.js", "sourceRoot": "", "sources": ["../../src/logging.ts"], "names": [], "mappings": ";;;AAAA,mCAAqC;AAErC,IAAY,QAKX;AALD,WAAY,QAAQ;IAClB,2BAAe,CAAA;IACf,yBAAa,CAAA;IACb,yBAAa,CAAA;IACb,2BAAe,CAAA;AACjB,CAAC,EALW,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAKnB;AAMD,SAAgB,iBAAiB,CAAC,IAAY;IAC5C,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE;QACnC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,IAAI,KAAK,GAAG,EAAE,OAAO,EAAE,SAAS,CAAC,CAAA;IACzD,CAAC,CAAA;AACH,CAAC;AAJD,8CAIC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAC,KAAe;IAC9C,QAAQ,KAAK,EAAE;QACb,KAAK,QAAQ,CAAC,KAAK;YACjB,OAAO,EAAE,CAAA;QACX,KAAK,QAAQ,CAAC,IAAI;YAChB,OAAO,EAAE,CAAA;QACX,KAAK,QAAQ,CAAC,IAAI;YAChB,OAAO,EAAE,CAAA;QACX,KAAK,QAAQ,CAAC,KAAK;YACjB,OAAO,EAAE,CAAA;QACX;YACE,OAAO,IAAA,mBAAW,EAAC,KAAK,CAAC,CAAA;KAC5B;AACH,CAAC;AAbD,4CAaC", "sourcesContent": ["import { assertNever } from \"./utils\"\n\nexport enum LogLevel {\n  DEBUG = \"debug\",\n  INFO = \"info\",\n  WARN = \"warn\",\n  ERROR = \"error\",\n}\n\nexport interface Logger {\n  (level: LogLevel, message: string, extraInfo: Record<string, unknown>): void\n}\n\nexport function makeConsoleLogger(name: string): Logger {\n  return (level, message, extraInfo) => {\n    console[level](`${name} ${level}:`, message, extraInfo)\n  }\n}\n\n/**\n * Transforms a log level into a comparable (numerical) value ordered by severity.\n */\nexport function logLevelSeverity(level: LogLevel): number {\n  switch (level) {\n    case LogLevel.DEBUG:\n      return 20\n    case LogLevel.INFO:\n      return 40\n    case LogLevel.WARN:\n      return 60\n    case LogLevel.ERROR:\n      return 80\n    default:\n      return assertNever(level)\n  }\n}\n"]}