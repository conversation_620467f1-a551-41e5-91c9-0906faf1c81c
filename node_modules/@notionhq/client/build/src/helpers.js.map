{"version": 3, "file": "helpers.js", "sourceRoot": "", "sources": ["../../src/helpers.ts"], "names": [], "mappings": ";;;AA2BA;;;;;;;;;;;;;;;;;;GAkBG;AACI,KAAK,SAAS,CAAC,CAAC,mBAAmB,CACxC,MAAoD,EACpD,aAAmB;IAEnB,IAAI,UAAU,GAA8B,aAAa,CAAC,YAAY,CAAA;IACtE,GAAG;QACD,MAAM,QAAQ,GAAwB,MAAM,MAAM,CAAC;YACjD,GAAG,aAAa;YAChB,YAAY,EAAE,UAAU;SACzB,CAAC,CAAA;QACF,KAAK,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAA;QACvB,UAAU,GAAG,QAAQ,CAAC,WAAW,CAAA;KAClC,QAAQ,UAAU,EAAC;AACtB,CAAC;AAbD,kDAaC;AAED;;;;;;;;;;;;;;;;;GAiBG;AACI,KAAK,UAAU,mBAAmB,CACvC,MAAoD,EACpD,aAAmB;IAEnB,MAAM,OAAO,GAAW,EAAE,CAAA;IAC1B,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,mBAAmB,CAAC,MAAM,EAAE,aAAa,CAAC,EAAE;QACnE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;KACnB;IACD,OAAO,OAAO,CAAA;AAChB,CAAC;AATD,kDASC;AAED;;GAEG;AACH,SAAgB,WAAW,CACzB,QAM8B;IAE9B,OAAO,QAAQ,CAAC,MAAM,KAAK,OAAO,IAAI,MAAM,IAAI,QAAQ,CAAA;AAC1D,CAAC;AAVD,kCAUC;AAED;;GAEG;AACH,SAAgB,UAAU,CACxB,QAM8B;IAE9B,OAAO,QAAQ,CAAC,MAAM,KAAK,MAAM,IAAI,KAAK,IAAI,QAAQ,CAAA;AACxD,CAAC;AAVD,gCAUC;AAED;;GAEG;AACH,SAAgB,cAAc,CAC5B,QAM8B;IAE9B,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU,IAAI,OAAO,IAAI,QAAQ,CAAA;AAC9D,CAAC;AAVD,wCAUC;AAED;;;GAGG;AACH,SAAgB,oBAAoB,CAClC,QAM8B;IAE9B,IAAI,QAAQ,CAAC,MAAM,KAAK,UAAU,EAAE;QAClC,OAAO,cAAc,CAAC,QAAQ,CAAC,CAAA;KAChC;SAAM;QACL,OAAO,UAAU,CAAC,QAAQ,CAAC,CAAA;KAC5B;AACH,CAAC;AAdD,oDAcC;AAED;;GAEG;AACH,SAAgB,UAAU,CACxB,QAAwD;IAExD,OAAO,MAAM,IAAI,QAAQ,CAAA;AAC3B,CAAC;AAJD,gCAIC;AAED;;GAEG;AACH,SAAgB,aAAa,CAC3B,QAA8D;IAE9D,OAAO,YAAY,IAAI,QAAQ,CAAA;AACjC,CAAC;AAJD,sCAIC;AAED;;GAEG;AACH,SAAgB,0BAA0B,CACxC,QAA8B;IAE9B,OAAO,QAAQ,CAAC,IAAI,KAAK,MAAM,CAAA;AACjC,CAAC;AAJD,gEAIC;AAED;;GAEG;AACH,SAAgB,8BAA8B,CAC5C,QAA8B;IAE9B,OAAO,QAAQ,CAAC,IAAI,KAAK,UAAU,CAAA;AACrC,CAAC;AAJD,wEAIC;AAED;;GAEG;AACH,SAAgB,6BAA6B,CAC3C,QAA8B;IAE9B,OAAO,QAAQ,CAAC,IAAI,KAAK,SAAS,CAAA;AACpC,CAAC;AAJD,sEAIC", "sourcesContent": ["import {\n  BlockObjectResponse,\n  CommentObjectResponse,\n  DatabaseObjectResponse,\n  EquationRichTextItemResponse,\n  PageObjectResponse,\n  PartialBlockObjectResponse,\n  PartialCommentObjectResponse,\n  PartialDatabaseObjectResponse,\n  PartialPageObjectResponse,\n  PartialUserObjectResponse,\n  RichTextItemResponse,\n  TextRichTextItemResponse,\n  UserObjectResponse,\n} from \"./api-endpoints\"\n\ninterface PaginatedArgs {\n  start_cursor?: string\n}\n\ninterface PaginatedList<T> {\n  object: \"list\"\n  results: T[]\n  next_cursor: string | null\n  has_more: boolean\n}\n\n/**\n * Returns an async iterator over the results of any paginated Notion API.\n *\n * Example (given a notion Client called `notion`):\n *\n * ```\n * for await (const block of iteratePaginatedAPI(notion.blocks.children.list, {\n *   block_id: parentBlockId,\n * })) {\n *   // Do something with block.\n * }\n * ```\n *\n * @param listFn A bound function on the Notion client that represents a conforming paginated\n *   API. Example: `notion.blocks.children.list`.\n * @param firstPageArgs Arguments that should be passed to the API on the first and subsequent\n *   calls to the API. Any necessary `next_cursor` will be automatically populated by\n *   this function. Example: `{ block_id: \"<my block id>\" }`\n */\nexport async function* iteratePaginatedAPI<Args extends PaginatedArgs, Item>(\n  listFn: (args: Args) => Promise<PaginatedList<Item>>,\n  firstPageArgs: Args\n): AsyncIterableIterator<Item> {\n  let nextCursor: string | null | undefined = firstPageArgs.start_cursor\n  do {\n    const response: PaginatedList<Item> = await listFn({\n      ...firstPageArgs,\n      start_cursor: nextCursor,\n    })\n    yield* response.results\n    nextCursor = response.next_cursor\n  } while (nextCursor)\n}\n\n/**\n * Collect all of the results of paginating an API into an in-memory array.\n *\n * Example (given a notion Client called `notion`):\n *\n * ```\n * const blocks = await collectPaginatedAPI(notion.blocks.children.list, {\n *   block_id: parentBlockId,\n * })\n * // Do something with blocks.\n * ```\n *\n * @param listFn A bound function on the Notion client that represents a conforming paginated\n *   API. Example: `notion.blocks.children.list`.\n * @param firstPageArgs Arguments that should be passed to the API on the first and subsequent\n *   calls to the API. Any necessary `next_cursor` will be automatically populated by\n *   this function. Example: `{ block_id: \"<my block id>\" }`\n */\nexport async function collectPaginatedAPI<Args extends PaginatedArgs, Item>(\n  listFn: (args: Args) => Promise<PaginatedList<Item>>,\n  firstPageArgs: Args\n): Promise<Item[]> {\n  const results: Item[] = []\n  for await (const item of iteratePaginatedAPI(listFn, firstPageArgs)) {\n    results.push(item)\n  }\n  return results\n}\n\n/**\n * @returns `true` if `response` is a full `BlockObjectResponse`.\n */\nexport function isFullBlock(\n  response:\n    | PageObjectResponse\n    | PartialPageObjectResponse\n    | DatabaseObjectResponse\n    | PartialDatabaseObjectResponse\n    | BlockObjectResponse\n    | PartialBlockObjectResponse\n): response is BlockObjectResponse {\n  return response.object === \"block\" && \"type\" in response\n}\n\n/**\n * @returns `true` if `response` is a full `PageObjectResponse`.\n */\nexport function isFullPage(\n  response:\n    | PageObjectResponse\n    | PartialPageObjectResponse\n    | DatabaseObjectResponse\n    | PartialDatabaseObjectResponse\n    | BlockObjectResponse\n    | PartialBlockObjectResponse\n): response is PageObjectResponse {\n  return response.object === \"page\" && \"url\" in response\n}\n\n/**\n * @returns `true` if `response` is a full `DatabaseObjectResponse`.\n */\nexport function isFullDatabase(\n  response:\n    | PageObjectResponse\n    | PartialPageObjectResponse\n    | DatabaseObjectResponse\n    | PartialDatabaseObjectResponse\n    | BlockObjectResponse\n    | PartialBlockObjectResponse\n): response is DatabaseObjectResponse {\n  return response.object === \"database\" && \"title\" in response\n}\n\n/**\n * @returns `true` if `response` is a full `DatabaseObjectResponse` or a full\n * `PageObjectResponse`.\n */\nexport function isFullPageOrDatabase(\n  response:\n    | PageObjectResponse\n    | PartialPageObjectResponse\n    | DatabaseObjectResponse\n    | PartialDatabaseObjectResponse\n    | BlockObjectResponse\n    | PartialBlockObjectResponse\n): response is DatabaseObjectResponse | PageObjectResponse {\n  if (response.object === \"database\") {\n    return isFullDatabase(response)\n  } else {\n    return isFullPage(response)\n  }\n}\n\n/**\n * @returns `true` if `response` is a full `UserObjectResponse`.\n */\nexport function isFullUser(\n  response: UserObjectResponse | PartialUserObjectResponse\n): response is UserObjectResponse {\n  return \"type\" in response\n}\n\n/**\n * @returns `true` if `response` is a full `CommentObjectResponse`.\n */\nexport function isFullComment(\n  response: CommentObjectResponse | PartialCommentObjectResponse\n): response is CommentObjectResponse {\n  return \"created_by\" in response\n}\n\n/**\n * @returns `true` if `richText` is a `TextRichTextItemResponse`.\n */\nexport function isTextRichTextItemResponse(\n  richText: RichTextItemResponse\n): richText is TextRichTextItemResponse {\n  return richText.type === \"text\"\n}\n\n/**\n * @returns `true` if `richText` is an `EquationRichTextItemResponse`.\n */\nexport function isEquationRichTextItemResponse(\n  richText: RichTextItemResponse\n): richText is EquationRichTextItemResponse {\n  return richText.type === \"equation\"\n}\n\n/**\n * @returns `true` if `richText` is an `MentionRichTextItemResponse`.\n */\nexport function isMentionRichTextItemResponse(\n  richText: RichTextItemResponse\n): richText is EquationRichTextItemResponse {\n  return richText.type === \"mention\"\n}\n"]}