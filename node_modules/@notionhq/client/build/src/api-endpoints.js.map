{"version": 3, "file": "api-endpoints.js", "sourceRoot": "", "sources": ["../../src/api-endpoints.ts"], "names": [], "mappings": ";AAAA,sBAAsB;AACtB,+CAA+C;;;AAq2TlC,QAAA,OAAO,GAAG;IACrB,MAAM,EAAE,KAAK;IACb,UAAU,EAAE,EAAE;IACd,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,EAAE;IACd,IAAI,EAAE,GAAW,EAAE,CAAC,UAAU;CACtB,CAAA;AAUG,QAAA,OAAO,GAAG;IACrB,MAAM,EAAE,KAAK;IACb,UAAU,EAAE,CAAC,SAAS,CAAC;IACvB,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,EAAE;IACd,IAAI,EAAE,CAAC,CAAwB,EAAU,EAAE,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE;CACxD,CAAA;AAkBG,QAAA,SAAS,GAAG;IACvB,MAAM,EAAE,KAAK;IACb,UAAU,EAAE,EAAE;IACd,WAAW,EAAE,CAAC,cAAc,EAAE,WAAW,CAAC;IAC1C,UAAU,EAAE,EAAE;IACd,IAAI,EAAE,GAAW,EAAE,CAAC,OAAO;CACnB,CAAA;AAgQG,QAAA,UAAU,GAAG;IACxB,MAAM,EAAE,MAAM;IACd,UAAU,EAAE,EAAE;IACd,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC;IAC5E,IAAI,EAAE,GAAW,EAAE,CAAC,OAAO;CACnB,CAAA;AAcG,QAAA,OAAO,GAAG;IACrB,MAAM,EAAE,KAAK;IACb,UAAU,EAAE,CAAC,SAAS,CAAC;IACvB,WAAW,EAAE,CAAC,mBAAmB,CAAC;IAClC,UAAU,EAAE,EAAE;IACd,IAAI,EAAE,CAAC,CAAwB,EAAU,EAAE,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE;CACxD,CAAA;AAkQG,QAAA,UAAU,GAAG;IACxB,MAAM,EAAE,OAAO;IACf,UAAU,EAAE,CAAC,SAAS,CAAC;IACvB,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;IACnE,IAAI,EAAE,CAAC,CAA2B,EAAU,EAAE,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE;CAC3D,CAAA;AAmBG,QAAA,eAAe,GAAG;IAC7B,MAAM,EAAE,KAAK;IACb,UAAU,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;IACtC,WAAW,EAAE,CAAC,cAAc,EAAE,WAAW,CAAC;IAC1C,UAAU,EAAE,EAAE;IACd,IAAI,EAAE,CAAC,CAAgC,EAAU,EAAE,CACjD,SAAS,CAAC,CAAC,OAAO,eAAe,CAAC,CAAC,WAAW,EAAE;CAC1C,CAAA;AAUG,QAAA,QAAQ,GAAG;IACtB,MAAM,EAAE,KAAK;IACb,UAAU,EAAE,CAAC,UAAU,CAAC;IACxB,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,EAAE;IACd,IAAI,EAAE,CAAC,CAAyB,EAAU,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE;CAC3D,CAAA;AA2OG,QAAA,WAAW,GAAG;IACzB,MAAM,EAAE,OAAO;IACf,UAAU,EAAE,CAAC,UAAU,CAAC;IACxB,WAAW,EAAE,EAAE;IACf,UAAU,EAAE;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;QACP,OAAO;QACP,KAAK;QACL,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,SAAS;QACT,YAAY;QACZ,mBAAmB;QACnB,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,oBAAoB;QACpB,oBAAoB;QACpB,OAAO;QACP,OAAO;QACP,QAAQ;QACR,UAAU;QACV,SAAS;QACT,cAAc;QACd,OAAO;KACR;IACD,IAAI,EAAE,CAAC,CAA4B,EAAU,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE;CAC9D,CAAA;AAYG,QAAA,WAAW,GAAG;IACzB,MAAM,EAAE,QAAQ;IAChB,UAAU,EAAE,CAAC,UAAU,CAAC;IACxB,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,EAAE;IACd,IAAI,EAAE,CAAC,CAA4B,EAAU,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE;CAC9D,CAAA;AAuBG,QAAA,iBAAiB,GAAG;IAC/B,MAAM,EAAE,KAAK;IACb,UAAU,EAAE,CAAC,UAAU,CAAC;IACxB,WAAW,EAAE,CAAC,cAAc,EAAE,WAAW,CAAC;IAC1C,UAAU,EAAE,EAAE;IACd,IAAI,EAAE,CAAC,CAAkC,EAAU,EAAE,CACnD,UAAU,CAAC,CAAC,QAAQ,WAAW;CACzB,CAAA;AAuBG,QAAA,mBAAmB,GAAG;IACjC,MAAM,EAAE,OAAO;IACf,UAAU,EAAE,CAAC,UAAU,CAAC;IACxB,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;IACjC,IAAI,EAAE,CAAC,CAAoC,EAAU,EAAE,CACrD,UAAU,CAAC,CAAC,QAAQ,WAAW;CACzB,CAAA;AAYG,QAAA,WAAW,GAAG;IACzB,MAAM,EAAE,KAAK;IACb,UAAU,EAAE,CAAC,aAAa,CAAC;IAC3B,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,EAAE;IACd,IAAI,EAAE,CAAC,CAA4B,EAAU,EAAE,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE;CACpE,CAAA;AAqPG,QAAA,cAAc,GAAG;IAC5B,MAAM,EAAE,OAAO;IACf,UAAU,EAAE,CAAC,aAAa,CAAC;IAC3B,WAAW,EAAE,EAAE;IACf,UAAU,EAAE;QACV,OAAO;QACP,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,WAAW;QACX,UAAU;QACV,UAAU;KACX;IACD,IAAI,EAAE,CAAC,CAA+B,EAAU,EAAE,CAChD,aAAa,CAAC,CAAC,WAAW,EAAE;CACtB,CAAA;AAgEG,QAAA,aAAa,GAAG;IAC3B,MAAM,EAAE,MAAM;IACd,UAAU,EAAE,CAAC,aAAa,CAAC;IAC3B,WAAW,EAAE,CAAC,mBAAmB,CAAC;IAClC,UAAU,EAAE;QACV,OAAO;QACP,QAAQ;QACR,cAAc;QACd,WAAW;QACX,UAAU;QACV,UAAU;KACX;IACD,IAAI,EAAE,CAAC,CAA8B,EAAU,EAAE,CAC/C,aAAa,CAAC,CAAC,WAAW,QAAQ;CAC5B,CAAA;AAkBG,QAAA,aAAa,GAAG;IAC3B,MAAM,EAAE,KAAK;IACb,UAAU,EAAE,EAAE;IACd,WAAW,EAAE,CAAC,cAAc,EAAE,WAAW,CAAC;IAC1C,UAAU,EAAE,EAAE;IACd,IAAI,EAAE,GAAW,EAAE,CAAC,WAAW;CACvB,CAAA;AAqLG,QAAA,cAAc,GAAG;IAC5B,MAAM,EAAE,MAAM;IACd,UAAU,EAAE,EAAE;IACd,WAAW,EAAE,EAAE;IACf,UAAU,EAAE;QACV,QAAQ;QACR,YAAY;QACZ,MAAM;QACN,OAAO;QACP,OAAO;QACP,aAAa;QACb,WAAW;KACZ;IACD,IAAI,EAAE,GAAW,EAAE,CAAC,WAAW;CACvB,CAAA;AA6BG,QAAA,MAAM,GAAG;IACpB,MAAM,EAAE,MAAM;IACd,UAAU,EAAE,EAAE;IACd,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,QAAQ,CAAC;IACpE,IAAI,EAAE,GAAW,EAAE,CAAC,QAAQ;CACpB,CAAA;AAeG,QAAA,aAAa,GAAG;IAC3B,MAAM,EAAE,MAAM;IACd,UAAU,EAAE,EAAE;IACd,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,eAAe,CAAC;IACpD,IAAI,EAAE,GAAW,EAAE,CAAC,UAAU;CACtB,CAAA;AAmBG,QAAA,YAAY,GAAG;IAC1B,MAAM,EAAE,KAAK;IACb,UAAU,EAAE,EAAE;IACd,WAAW,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,WAAW,CAAC;IACtD,UAAU,EAAE,EAAE;IACd,IAAI,EAAE,GAAW,EAAE,CAAC,UAAU;CACtB,CAAA;AAoCG,QAAA,UAAU,GAAG;IACxB,MAAM,EAAE,MAAM;IACd,UAAU,EAAE,EAAE;IACd,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,cAAc,EAAE,kBAAkB,CAAC;IACtE,IAAI,EAAE,GAAW,EAAE,CAAC,aAAa;CACzB,CAAA;AAQG,QAAA,WAAW,GAAG;IACzB,MAAM,EAAE,MAAM;IACd,UAAU,EAAE,EAAE;IACd,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,CAAC,OAAO,CAAC;IACrB,IAAI,EAAE,GAAW,EAAE,CAAC,cAAc;CAC1B,CAAA;AAYG,QAAA,eAAe,GAAG;IAC7B,MAAM,EAAE,MAAM;IACd,UAAU,EAAE,EAAE;IACd,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,CAAC,OAAO,CAAC;IACrB,IAAI,EAAE,GAAW,EAAE,CAAC,kBAAkB;CAC9B,CAAA", "sourcesContent": ["// cspell:disable-file\n// Note: This is a generated file. DO NOT EDIT!\n\ntype IdRequest = string | string\n\nexport type PersonUserObjectResponse = {\n  type: \"person\"\n  person: { email?: string }\n  name: string | null\n  avatar_url: string | null\n  id: IdRequest\n  object: \"user\"\n}\n\ntype EmptyObject = Record<string, never>\n\nexport type PartialUserObjectResponse = { id: IdRequest; object: \"user\" }\n\nexport type BotUserObjectResponse = {\n  type: \"bot\"\n  bot:\n    | EmptyObject\n    | {\n        owner:\n          | {\n              type: \"user\"\n              user:\n                | {\n                    type: \"person\"\n                    person: { email: string }\n                    name: string | null\n                    avatar_url: string | null\n                    id: IdRequest\n                    object: \"user\"\n                  }\n                | PartialUserObjectResponse\n            }\n          | { type: \"workspace\"; workspace: true }\n        workspace_name: string | null\n      }\n  name: string | null\n  avatar_url: string | null\n  id: IdRequest\n  object: \"user\"\n}\n\nexport type UserObjectResponse =\n  | PersonUserObjectResponse\n  | BotUserObjectResponse\n\ntype SelectColor =\n  | \"default\"\n  | \"gray\"\n  | \"brown\"\n  | \"orange\"\n  | \"yellow\"\n  | \"green\"\n  | \"blue\"\n  | \"purple\"\n  | \"pink\"\n  | \"red\"\n\ntype PartialSelectResponse = { id: string; name: string; color: SelectColor }\n\ntype TimeZoneRequest =\n  | \"Africa/Abidjan\"\n  | \"Africa/Accra\"\n  | \"Africa/Addis_Ababa\"\n  | \"Africa/Algiers\"\n  | \"Africa/Asmara\"\n  | \"Africa/Asmera\"\n  | \"Africa/Bamako\"\n  | \"Africa/Bangui\"\n  | \"Africa/Banjul\"\n  | \"Africa/Bissau\"\n  | \"Africa/Blantyre\"\n  | \"Africa/Brazzaville\"\n  | \"Africa/Bujumbura\"\n  | \"Africa/Cairo\"\n  | \"Africa/Casablanca\"\n  | \"Africa/Ceuta\"\n  | \"Africa/Conakry\"\n  | \"Africa/Dakar\"\n  | \"Africa/Dar_es_Salaam\"\n  | \"Africa/Djibouti\"\n  | \"Africa/Douala\"\n  | \"Africa/El_Aaiun\"\n  | \"Africa/Freetown\"\n  | \"Africa/Gaborone\"\n  | \"Africa/Harare\"\n  | \"Africa/Johannesburg\"\n  | \"Africa/Juba\"\n  | \"Africa/Kampala\"\n  | \"Africa/Khartoum\"\n  | \"Africa/Kigali\"\n  | \"Africa/Kinshasa\"\n  | \"Africa/Lagos\"\n  | \"Africa/Libreville\"\n  | \"Africa/Lome\"\n  | \"Africa/Luanda\"\n  | \"Africa/Lubumbashi\"\n  | \"Africa/Lusaka\"\n  | \"Africa/Malabo\"\n  | \"Africa/Maputo\"\n  | \"Africa/Maseru\"\n  | \"Africa/Mbabane\"\n  | \"Africa/Mogadishu\"\n  | \"Africa/Monrovia\"\n  | \"Africa/Nairobi\"\n  | \"Africa/Ndjamena\"\n  | \"Africa/Niamey\"\n  | \"Africa/Nouakchott\"\n  | \"Africa/Ouagadougou\"\n  | \"Africa/Porto-Novo\"\n  | \"Africa/Sao_Tome\"\n  | \"Africa/Timbuktu\"\n  | \"Africa/Tripoli\"\n  | \"Africa/Tunis\"\n  | \"Africa/Windhoek\"\n  | \"America/Adak\"\n  | \"America/Anchorage\"\n  | \"America/Anguilla\"\n  | \"America/Antigua\"\n  | \"America/Araguaina\"\n  | \"America/Argentina/Buenos_Aires\"\n  | \"America/Argentina/Catamarca\"\n  | \"America/Argentina/ComodRivadavia\"\n  | \"America/Argentina/Cordoba\"\n  | \"America/Argentina/Jujuy\"\n  | \"America/Argentina/La_Rioja\"\n  | \"America/Argentina/Mendoza\"\n  | \"America/Argentina/Rio_Gallegos\"\n  | \"America/Argentina/Salta\"\n  | \"America/Argentina/San_Juan\"\n  | \"America/Argentina/San_Luis\"\n  | \"America/Argentina/Tucuman\"\n  | \"America/Argentina/Ushuaia\"\n  | \"America/Aruba\"\n  | \"America/Asuncion\"\n  | \"America/Atikokan\"\n  | \"America/Atka\"\n  | \"America/Bahia\"\n  | \"America/Bahia_Banderas\"\n  | \"America/Barbados\"\n  | \"America/Belem\"\n  | \"America/Belize\"\n  | \"America/Blanc-Sablon\"\n  | \"America/Boa_Vista\"\n  | \"America/Bogota\"\n  | \"America/Boise\"\n  | \"America/Buenos_Aires\"\n  | \"America/Cambridge_Bay\"\n  | \"America/Campo_Grande\"\n  | \"America/Cancun\"\n  | \"America/Caracas\"\n  | \"America/Catamarca\"\n  | \"America/Cayenne\"\n  | \"America/Cayman\"\n  | \"America/Chicago\"\n  | \"America/Chihuahua\"\n  | \"America/Ciudad_Juarez\"\n  | \"America/Coral_Harbour\"\n  | \"America/Cordoba\"\n  | \"America/Costa_Rica\"\n  | \"America/Creston\"\n  | \"America/Cuiaba\"\n  | \"America/Curacao\"\n  | \"America/Danmarkshavn\"\n  | \"America/Dawson\"\n  | \"America/Dawson_Creek\"\n  | \"America/Denver\"\n  | \"America/Detroit\"\n  | \"America/Dominica\"\n  | \"America/Edmonton\"\n  | \"America/Eirunepe\"\n  | \"America/El_Salvador\"\n  | \"America/Ensenada\"\n  | \"America/Fort_Nelson\"\n  | \"America/Fort_Wayne\"\n  | \"America/Fortaleza\"\n  | \"America/Glace_Bay\"\n  | \"America/Godthab\"\n  | \"America/Goose_Bay\"\n  | \"America/Grand_Turk\"\n  | \"America/Grenada\"\n  | \"America/Guadeloupe\"\n  | \"America/Guatemala\"\n  | \"America/Guayaquil\"\n  | \"America/Guyana\"\n  | \"America/Halifax\"\n  | \"America/Havana\"\n  | \"America/Hermosillo\"\n  | \"America/Indiana/Indianapolis\"\n  | \"America/Indiana/Knox\"\n  | \"America/Indiana/Marengo\"\n  | \"America/Indiana/Petersburg\"\n  | \"America/Indiana/Tell_City\"\n  | \"America/Indiana/Vevay\"\n  | \"America/Indiana/Vincennes\"\n  | \"America/Indiana/Winamac\"\n  | \"America/Indianapolis\"\n  | \"America/Inuvik\"\n  | \"America/Iqaluit\"\n  | \"America/Jamaica\"\n  | \"America/Jujuy\"\n  | \"America/Juneau\"\n  | \"America/Kentucky/Louisville\"\n  | \"America/Kentucky/Monticello\"\n  | \"America/Knox_IN\"\n  | \"America/Kralendijk\"\n  | \"America/La_Paz\"\n  | \"America/Lima\"\n  | \"America/Los_Angeles\"\n  | \"America/Louisville\"\n  | \"America/Lower_Princes\"\n  | \"America/Maceio\"\n  | \"America/Managua\"\n  | \"America/Manaus\"\n  | \"America/Marigot\"\n  | \"America/Martinique\"\n  | \"America/Matamoros\"\n  | \"America/Mazatlan\"\n  | \"America/Mendoza\"\n  | \"America/Menominee\"\n  | \"America/Merida\"\n  | \"America/Metlakatla\"\n  | \"America/Mexico_City\"\n  | \"America/Miquelon\"\n  | \"America/Moncton\"\n  | \"America/Monterrey\"\n  | \"America/Montevideo\"\n  | \"America/Montreal\"\n  | \"America/Montserrat\"\n  | \"America/Nassau\"\n  | \"America/New_York\"\n  | \"America/Nipigon\"\n  | \"America/Nome\"\n  | \"America/Noronha\"\n  | \"America/North_Dakota/Beulah\"\n  | \"America/North_Dakota/Center\"\n  | \"America/North_Dakota/New_Salem\"\n  | \"America/Nuuk\"\n  | \"America/Ojinaga\"\n  | \"America/Panama\"\n  | \"America/Pangnirtung\"\n  | \"America/Paramaribo\"\n  | \"America/Phoenix\"\n  | \"America/Port-au-Prince\"\n  | \"America/Port_of_Spain\"\n  | \"America/Porto_Acre\"\n  | \"America/Porto_Velho\"\n  | \"America/Puerto_Rico\"\n  | \"America/Punta_Arenas\"\n  | \"America/Rainy_River\"\n  | \"America/Rankin_Inlet\"\n  | \"America/Recife\"\n  | \"America/Regina\"\n  | \"America/Resolute\"\n  | \"America/Rio_Branco\"\n  | \"America/Rosario\"\n  | \"America/Santa_Isabel\"\n  | \"America/Santarem\"\n  | \"America/Santiago\"\n  | \"America/Santo_Domingo\"\n  | \"America/Sao_Paulo\"\n  | \"America/Scoresbysund\"\n  | \"America/Shiprock\"\n  | \"America/Sitka\"\n  | \"America/St_Barthelemy\"\n  | \"America/St_Johns\"\n  | \"America/St_Kitts\"\n  | \"America/St_Lucia\"\n  | \"America/St_Thomas\"\n  | \"America/St_Vincent\"\n  | \"America/Swift_Current\"\n  | \"America/Tegucigalpa\"\n  | \"America/Thule\"\n  | \"America/Thunder_Bay\"\n  | \"America/Tijuana\"\n  | \"America/Toronto\"\n  | \"America/Tortola\"\n  | \"America/Vancouver\"\n  | \"America/Virgin\"\n  | \"America/Whitehorse\"\n  | \"America/Winnipeg\"\n  | \"America/Yakutat\"\n  | \"America/Yellowknife\"\n  | \"Antarctica/Casey\"\n  | \"Antarctica/Davis\"\n  | \"Antarctica/DumontDUrville\"\n  | \"Antarctica/Macquarie\"\n  | \"Antarctica/Mawson\"\n  | \"Antarctica/McMurdo\"\n  | \"Antarctica/Palmer\"\n  | \"Antarctica/Rothera\"\n  | \"Antarctica/South_Pole\"\n  | \"Antarctica/Syowa\"\n  | \"Antarctica/Troll\"\n  | \"Antarctica/Vostok\"\n  | \"Arctic/Longyearbyen\"\n  | \"Asia/Aden\"\n  | \"Asia/Almaty\"\n  | \"Asia/Amman\"\n  | \"Asia/Anadyr\"\n  | \"Asia/Aqtau\"\n  | \"Asia/Aqtobe\"\n  | \"Asia/Ashgabat\"\n  | \"Asia/Ashkhabad\"\n  | \"Asia/Atyrau\"\n  | \"Asia/Baghdad\"\n  | \"Asia/Bahrain\"\n  | \"Asia/Baku\"\n  | \"Asia/Bangkok\"\n  | \"Asia/Barnaul\"\n  | \"Asia/Beirut\"\n  | \"Asia/Bishkek\"\n  | \"Asia/Brunei\"\n  | \"Asia/Calcutta\"\n  | \"Asia/Chita\"\n  | \"Asia/Choibalsan\"\n  | \"Asia/Chongqing\"\n  | \"Asia/Chungking\"\n  | \"Asia/Colombo\"\n  | \"Asia/Dacca\"\n  | \"Asia/Damascus\"\n  | \"Asia/Dhaka\"\n  | \"Asia/Dili\"\n  | \"Asia/Dubai\"\n  | \"Asia/Dushanbe\"\n  | \"Asia/Famagusta\"\n  | \"Asia/Gaza\"\n  | \"Asia/Harbin\"\n  | \"Asia/Hebron\"\n  | \"Asia/Ho_Chi_Minh\"\n  | \"Asia/Hong_Kong\"\n  | \"Asia/Hovd\"\n  | \"Asia/Irkutsk\"\n  | \"Asia/Istanbul\"\n  | \"Asia/Jakarta\"\n  | \"Asia/Jayapura\"\n  | \"Asia/Jerusalem\"\n  | \"Asia/Kabul\"\n  | \"Asia/Kamchatka\"\n  | \"Asia/Karachi\"\n  | \"Asia/Kashgar\"\n  | \"Asia/Kathmandu\"\n  | \"Asia/Katmandu\"\n  | \"Asia/Khandyga\"\n  | \"Asia/Kolkata\"\n  | \"Asia/Krasnoyarsk\"\n  | \"Asia/Kuala_Lumpur\"\n  | \"Asia/Kuching\"\n  | \"Asia/Kuwait\"\n  | \"Asia/Macao\"\n  | \"Asia/Macau\"\n  | \"Asia/Magadan\"\n  | \"Asia/Makassar\"\n  | \"Asia/Manila\"\n  | \"Asia/Muscat\"\n  | \"Asia/Nicosia\"\n  | \"Asia/Novokuznetsk\"\n  | \"Asia/Novosibirsk\"\n  | \"Asia/Omsk\"\n  | \"Asia/Oral\"\n  | \"Asia/Phnom_Penh\"\n  | \"Asia/Pontianak\"\n  | \"Asia/Pyongyang\"\n  | \"Asia/Qatar\"\n  | \"Asia/Qostanay\"\n  | \"Asia/Qyzylorda\"\n  | \"Asia/Rangoon\"\n  | \"Asia/Riyadh\"\n  | \"Asia/Saigon\"\n  | \"Asia/Sakhalin\"\n  | \"Asia/Samarkand\"\n  | \"Asia/Seoul\"\n  | \"Asia/Shanghai\"\n  | \"Asia/Singapore\"\n  | \"Asia/Srednekolymsk\"\n  | \"Asia/Taipei\"\n  | \"Asia/Tashkent\"\n  | \"Asia/Tbilisi\"\n  | \"Asia/Tehran\"\n  | \"Asia/Tel_Aviv\"\n  | \"Asia/Thimbu\"\n  | \"Asia/Thimphu\"\n  | \"Asia/Tokyo\"\n  | \"Asia/Tomsk\"\n  | \"Asia/Ujung_Pandang\"\n  | \"Asia/Ulaanbaatar\"\n  | \"Asia/Ulan_Bator\"\n  | \"Asia/Urumqi\"\n  | \"Asia/Ust-Nera\"\n  | \"Asia/Vientiane\"\n  | \"Asia/Vladivostok\"\n  | \"Asia/Yakutsk\"\n  | \"Asia/Yangon\"\n  | \"Asia/Yekaterinburg\"\n  | \"Asia/Yerevan\"\n  | \"Atlantic/Azores\"\n  | \"Atlantic/Bermuda\"\n  | \"Atlantic/Canary\"\n  | \"Atlantic/Cape_Verde\"\n  | \"Atlantic/Faeroe\"\n  | \"Atlantic/Faroe\"\n  | \"Atlantic/Jan_Mayen\"\n  | \"Atlantic/Madeira\"\n  | \"Atlantic/Reykjavik\"\n  | \"Atlantic/South_Georgia\"\n  | \"Atlantic/St_Helena\"\n  | \"Atlantic/Stanley\"\n  | \"Australia/ACT\"\n  | \"Australia/Adelaide\"\n  | \"Australia/Brisbane\"\n  | \"Australia/Broken_Hill\"\n  | \"Australia/Canberra\"\n  | \"Australia/Currie\"\n  | \"Australia/Darwin\"\n  | \"Australia/Eucla\"\n  | \"Australia/Hobart\"\n  | \"Australia/LHI\"\n  | \"Australia/Lindeman\"\n  | \"Australia/Lord_Howe\"\n  | \"Australia/Melbourne\"\n  | \"Australia/NSW\"\n  | \"Australia/North\"\n  | \"Australia/Perth\"\n  | \"Australia/Queensland\"\n  | \"Australia/South\"\n  | \"Australia/Sydney\"\n  | \"Australia/Tasmania\"\n  | \"Australia/Victoria\"\n  | \"Australia/West\"\n  | \"Australia/Yancowinna\"\n  | \"Brazil/Acre\"\n  | \"Brazil/DeNoronha\"\n  | \"Brazil/East\"\n  | \"Brazil/West\"\n  | \"CET\"\n  | \"CST6CDT\"\n  | \"Canada/Atlantic\"\n  | \"Canada/Central\"\n  | \"Canada/Eastern\"\n  | \"Canada/Mountain\"\n  | \"Canada/Newfoundland\"\n  | \"Canada/Pacific\"\n  | \"Canada/Saskatchewan\"\n  | \"Canada/Yukon\"\n  | \"Chile/Continental\"\n  | \"Chile/EasterIsland\"\n  | \"Cuba\"\n  | \"EET\"\n  | \"EST\"\n  | \"EST5EDT\"\n  | \"Egypt\"\n  | \"Eire\"\n  | \"Etc/GMT\"\n  | \"Etc/GMT+0\"\n  | \"Etc/GMT+1\"\n  | \"Etc/GMT+10\"\n  | \"Etc/GMT+11\"\n  | \"Etc/GMT+12\"\n  | \"Etc/GMT+2\"\n  | \"Etc/GMT+3\"\n  | \"Etc/GMT+4\"\n  | \"Etc/GMT+5\"\n  | \"Etc/GMT+6\"\n  | \"Etc/GMT+7\"\n  | \"Etc/GMT+8\"\n  | \"Etc/GMT+9\"\n  | \"Etc/GMT-0\"\n  | \"Etc/GMT-1\"\n  | \"Etc/GMT-10\"\n  | \"Etc/GMT-11\"\n  | \"Etc/GMT-12\"\n  | \"Etc/GMT-13\"\n  | \"Etc/GMT-14\"\n  | \"Etc/GMT-2\"\n  | \"Etc/GMT-3\"\n  | \"Etc/GMT-4\"\n  | \"Etc/GMT-5\"\n  | \"Etc/GMT-6\"\n  | \"Etc/GMT-7\"\n  | \"Etc/GMT-8\"\n  | \"Etc/GMT-9\"\n  | \"Etc/GMT0\"\n  | \"Etc/Greenwich\"\n  | \"Etc/UCT\"\n  | \"Etc/UTC\"\n  | \"Etc/Universal\"\n  | \"Etc/Zulu\"\n  | \"Europe/Amsterdam\"\n  | \"Europe/Andorra\"\n  | \"Europe/Astrakhan\"\n  | \"Europe/Athens\"\n  | \"Europe/Belfast\"\n  | \"Europe/Belgrade\"\n  | \"Europe/Berlin\"\n  | \"Europe/Bratislava\"\n  | \"Europe/Brussels\"\n  | \"Europe/Bucharest\"\n  | \"Europe/Budapest\"\n  | \"Europe/Busingen\"\n  | \"Europe/Chisinau\"\n  | \"Europe/Copenhagen\"\n  | \"Europe/Dublin\"\n  | \"Europe/Gibraltar\"\n  | \"Europe/Guernsey\"\n  | \"Europe/Helsinki\"\n  | \"Europe/Isle_of_Man\"\n  | \"Europe/Istanbul\"\n  | \"Europe/Jersey\"\n  | \"Europe/Kaliningrad\"\n  | \"Europe/Kiev\"\n  | \"Europe/Kirov\"\n  | \"Europe/Kyiv\"\n  | \"Europe/Lisbon\"\n  | \"Europe/Ljubljana\"\n  | \"Europe/London\"\n  | \"Europe/Luxembourg\"\n  | \"Europe/Madrid\"\n  | \"Europe/Malta\"\n  | \"Europe/Mariehamn\"\n  | \"Europe/Minsk\"\n  | \"Europe/Monaco\"\n  | \"Europe/Moscow\"\n  | \"Europe/Nicosia\"\n  | \"Europe/Oslo\"\n  | \"Europe/Paris\"\n  | \"Europe/Podgorica\"\n  | \"Europe/Prague\"\n  | \"Europe/Riga\"\n  | \"Europe/Rome\"\n  | \"Europe/Samara\"\n  | \"Europe/San_Marino\"\n  | \"Europe/Sarajevo\"\n  | \"Europe/Saratov\"\n  | \"Europe/Simferopol\"\n  | \"Europe/Skopje\"\n  | \"Europe/Sofia\"\n  | \"Europe/Stockholm\"\n  | \"Europe/Tallinn\"\n  | \"Europe/Tirane\"\n  | \"Europe/Tiraspol\"\n  | \"Europe/Ulyanovsk\"\n  | \"Europe/Uzhgorod\"\n  | \"Europe/Vaduz\"\n  | \"Europe/Vatican\"\n  | \"Europe/Vienna\"\n  | \"Europe/Vilnius\"\n  | \"Europe/Volgograd\"\n  | \"Europe/Warsaw\"\n  | \"Europe/Zagreb\"\n  | \"Europe/Zaporozhye\"\n  | \"Europe/Zurich\"\n  | \"GB\"\n  | \"GB-Eire\"\n  | \"GMT\"\n  | \"GMT+0\"\n  | \"GMT-0\"\n  | \"GMT0\"\n  | \"Greenwich\"\n  | \"HST\"\n  | \"Hongkong\"\n  | \"Iceland\"\n  | \"Indian/Antananarivo\"\n  | \"Indian/Chagos\"\n  | \"Indian/Christmas\"\n  | \"Indian/Cocos\"\n  | \"Indian/Comoro\"\n  | \"Indian/Kerguelen\"\n  | \"Indian/Mahe\"\n  | \"Indian/Maldives\"\n  | \"Indian/Mauritius\"\n  | \"Indian/Mayotte\"\n  | \"Indian/Reunion\"\n  | \"Iran\"\n  | \"Israel\"\n  | \"Jamaica\"\n  | \"Japan\"\n  | \"Kwajalein\"\n  | \"Libya\"\n  | \"MET\"\n  | \"MST\"\n  | \"MST7MDT\"\n  | \"Mexico/BajaNorte\"\n  | \"Mexico/BajaSur\"\n  | \"Mexico/General\"\n  | \"NZ\"\n  | \"NZ-CHAT\"\n  | \"Navajo\"\n  | \"PRC\"\n  | \"PST8PDT\"\n  | \"Pacific/Apia\"\n  | \"Pacific/Auckland\"\n  | \"Pacific/Bougainville\"\n  | \"Pacific/Chatham\"\n  | \"Pacific/Chuuk\"\n  | \"Pacific/Easter\"\n  | \"Pacific/Efate\"\n  | \"Pacific/Enderbury\"\n  | \"Pacific/Fakaofo\"\n  | \"Pacific/Fiji\"\n  | \"Pacific/Funafuti\"\n  | \"Pacific/Galapagos\"\n  | \"Pacific/Gambier\"\n  | \"Pacific/Guadalcanal\"\n  | \"Pacific/Guam\"\n  | \"Pacific/Honolulu\"\n  | \"Pacific/Johnston\"\n  | \"Pacific/Kanton\"\n  | \"Pacific/Kiritimati\"\n  | \"Pacific/Kosrae\"\n  | \"Pacific/Kwajalein\"\n  | \"Pacific/Majuro\"\n  | \"Pacific/Marquesas\"\n  | \"Pacific/Midway\"\n  | \"Pacific/Nauru\"\n  | \"Pacific/Niue\"\n  | \"Pacific/Norfolk\"\n  | \"Pacific/Noumea\"\n  | \"Pacific/Pago_Pago\"\n  | \"Pacific/Palau\"\n  | \"Pacific/Pitcairn\"\n  | \"Pacific/Pohnpei\"\n  | \"Pacific/Ponape\"\n  | \"Pacific/Port_Moresby\"\n  | \"Pacific/Rarotonga\"\n  | \"Pacific/Saipan\"\n  | \"Pacific/Samoa\"\n  | \"Pacific/Tahiti\"\n  | \"Pacific/Tarawa\"\n  | \"Pacific/Tongatapu\"\n  | \"Pacific/Truk\"\n  | \"Pacific/Wake\"\n  | \"Pacific/Wallis\"\n  | \"Pacific/Yap\"\n  | \"Poland\"\n  | \"Portugal\"\n  | \"ROC\"\n  | \"ROK\"\n  | \"Singapore\"\n  | \"Turkey\"\n  | \"UCT\"\n  | \"US/Alaska\"\n  | \"US/Aleutian\"\n  | \"US/Arizona\"\n  | \"US/Central\"\n  | \"US/East-Indiana\"\n  | \"US/Eastern\"\n  | \"US/Hawaii\"\n  | \"US/Indiana-Starke\"\n  | \"US/Michigan\"\n  | \"US/Mountain\"\n  | \"US/Pacific\"\n  | \"US/Pacific-New\"\n  | \"US/Samoa\"\n  | \"UTC\"\n  | \"Universal\"\n  | \"W-SU\"\n  | \"WET\"\n  | \"Zulu\"\n\ntype DateResponse = {\n  start: string\n  end: string | null\n  time_zone: TimeZoneRequest | null\n}\n\ntype StringRequest = string\n\ntype TextRequest = string\n\ntype StringFormulaPropertyResponse = { type: \"string\"; string: string | null }\n\ntype DateFormulaPropertyResponse = { type: \"date\"; date: DateResponse | null }\n\ntype NumberFormulaPropertyResponse = { type: \"number\"; number: number | null }\n\ntype BooleanFormulaPropertyResponse = {\n  type: \"boolean\"\n  boolean: boolean | null\n}\n\ntype FormulaPropertyResponse =\n  | StringFormulaPropertyResponse\n  | DateFormulaPropertyResponse\n  | NumberFormulaPropertyResponse\n  | BooleanFormulaPropertyResponse\n\ntype VerificationPropertyUnverifiedResponse = {\n  state: \"unverified\"\n  date: null\n  verified_by: null\n}\n\ntype VerificationPropertyResponse = {\n  state: \"verified\" | \"expired\"\n  date: DateResponse | null\n  verified_by:\n    | { id: IdRequest }\n    | null\n    | {\n        person: { email?: string }\n        id: IdRequest\n        type?: \"person\"\n        name?: string | null\n        avatar_url?: string | null\n        object?: \"user\"\n      }\n    | null\n    | {\n        bot:\n          | EmptyObject\n          | {\n              owner:\n                | {\n                    type: \"user\"\n                    user:\n                      | {\n                          type: \"person\"\n                          person: { email: string }\n                          name: string | null\n                          avatar_url: string | null\n                          id: IdRequest\n                          object: \"user\"\n                        }\n                      | PartialUserObjectResponse\n                  }\n                | { type: \"workspace\"; workspace: true }\n              workspace_name: string | null\n            }\n        id: IdRequest\n        type?: \"bot\"\n        name?: string | null\n        avatar_url?: string | null\n        object?: \"user\"\n      }\n    | null\n}\n\ntype AnnotationResponse = {\n  bold: boolean\n  italic: boolean\n  strikethrough: boolean\n  underline: boolean\n  code: boolean\n  color:\n    | \"default\"\n    | \"gray\"\n    | \"brown\"\n    | \"orange\"\n    | \"yellow\"\n    | \"green\"\n    | \"blue\"\n    | \"purple\"\n    | \"pink\"\n    | \"red\"\n    | \"default_background\"\n    | \"gray_background\"\n    | \"brown_background\"\n    | \"orange_background\"\n    | \"yellow_background\"\n    | \"green_background\"\n    | \"blue_background\"\n    | \"purple_background\"\n    | \"pink_background\"\n    | \"red_background\"\n}\n\nexport type TextRichTextItemResponse = {\n  type: \"text\"\n  text: { content: string; link: { url: TextRequest } | null }\n  annotations: AnnotationResponse\n  plain_text: string\n  href: string | null\n}\n\ntype LinkPreviewMentionResponse = { url: TextRequest }\n\ntype LinkMentionResponse = {\n  href: string\n  title?: string\n  description?: string\n  link_author?: string\n  link_provider?: string\n  thumbnail_url?: string\n  icon_url?: string\n  iframe_url?: string\n  height?: number\n  padding?: number\n  padding_top?: number\n}\n\ntype TemplateMentionDateTemplateMentionResponse = {\n  type: \"template_mention_date\"\n  template_mention_date: \"today\" | \"now\"\n}\n\ntype TemplateMentionUserTemplateMentionResponse = {\n  type: \"template_mention_user\"\n  template_mention_user: \"me\"\n}\n\ntype TemplateMentionResponse =\n  | TemplateMentionDateTemplateMentionResponse\n  | TemplateMentionUserTemplateMentionResponse\n\ntype CustomEmojiResponse = { id: IdRequest; name: string; url: string }\n\nexport type MentionRichTextItemResponse = {\n  type: \"mention\"\n  mention:\n    | { type: \"user\"; user: PartialUserObjectResponse | UserObjectResponse }\n    | { type: \"date\"; date: DateResponse }\n    | { type: \"link_preview\"; link_preview: LinkPreviewMentionResponse }\n    | { type: \"link_mention\"; link_mention: LinkMentionResponse }\n    | { type: \"template_mention\"; template_mention: TemplateMentionResponse }\n    | { type: \"page\"; page: { id: IdRequest } }\n    | { type: \"database\"; database: { id: IdRequest } }\n    | { type: \"custom_emoji\"; custom_emoji: CustomEmojiResponse }\n  annotations: AnnotationResponse\n  plain_text: string\n  href: string | null\n}\n\nexport type EquationRichTextItemResponse = {\n  type: \"equation\"\n  equation: { expression: TextRequest }\n  annotations: AnnotationResponse\n  plain_text: string\n  href: string | null\n}\n\nexport type RichTextItemResponse =\n  | TextRichTextItemResponse\n  | MentionRichTextItemResponse\n  | EquationRichTextItemResponse\n\ntype RollupFunction =\n  | \"count\"\n  | \"count_values\"\n  | \"empty\"\n  | \"not_empty\"\n  | \"unique\"\n  | \"show_unique\"\n  | \"percent_empty\"\n  | \"percent_not_empty\"\n  | \"sum\"\n  | \"average\"\n  | \"median\"\n  | \"min\"\n  | \"max\"\n  | \"range\"\n  | \"earliest_date\"\n  | \"latest_date\"\n  | \"date_range\"\n  | \"checked\"\n  | \"unchecked\"\n  | \"percent_checked\"\n  | \"percent_unchecked\"\n  | \"count_per_group\"\n  | \"percent_per_group\"\n  | \"show_original\"\n\ntype EmojiRequest =\n  | \"😀\"\n  | \"😃\"\n  | \"😄\"\n  | \"😁\"\n  | \"😆\"\n  | \"😅\"\n  | \"🤣\"\n  | \"😂\"\n  | \"🙂\"\n  | \"🙃\"\n  | \"🫠\"\n  | \"😉\"\n  | \"😊\"\n  | \"😇\"\n  | \"🥰\"\n  | \"😍\"\n  | \"🤩\"\n  | \"😘\"\n  | \"😗\"\n  | \"☺️\"\n  | \"☺\"\n  | \"😚\"\n  | \"😙\"\n  | \"🥲\"\n  | \"😋\"\n  | \"😛\"\n  | \"😜\"\n  | \"🤪\"\n  | \"😝\"\n  | \"🤑\"\n  | \"🤗\"\n  | \"🤭\"\n  | \"🫢\"\n  | \"🫣\"\n  | \"🤫\"\n  | \"🤔\"\n  | \"🫡\"\n  | \"🤐\"\n  | \"🤨\"\n  | \"😐\"\n  | \"😑\"\n  | \"😶\"\n  | \"🫥\"\n  | \"😶‍🌫️\"\n  | \"😶‍🌫\"\n  | \"😏\"\n  | \"😒\"\n  | \"🙄\"\n  | \"😬\"\n  | \"😮‍💨\"\n  | \"🤥\"\n  | \"😌\"\n  | \"😔\"\n  | \"😪\"\n  | \"🤤\"\n  | \"😴\"\n  | \"😷\"\n  | \"🤒\"\n  | \"🤕\"\n  | \"🤢\"\n  | \"🤮\"\n  | \"🤧\"\n  | \"🥵\"\n  | \"🥶\"\n  | \"🥴\"\n  | \"😵\"\n  | \"😵‍💫\"\n  | \"🤯\"\n  | \"🤠\"\n  | \"🥳\"\n  | \"🥸\"\n  | \"😎\"\n  | \"🤓\"\n  | \"🧐\"\n  | \"😕\"\n  | \"🫤\"\n  | \"😟\"\n  | \"🙁\"\n  | \"☹️\"\n  | \"☹\"\n  | \"😮\"\n  | \"😯\"\n  | \"😲\"\n  | \"😳\"\n  | \"🥺\"\n  | \"🥹\"\n  | \"😦\"\n  | \"😧\"\n  | \"😨\"\n  | \"😰\"\n  | \"😥\"\n  | \"😢\"\n  | \"😭\"\n  | \"😱\"\n  | \"😖\"\n  | \"😣\"\n  | \"😞\"\n  | \"😓\"\n  | \"😩\"\n  | \"😫\"\n  | \"🥱\"\n  | \"😤\"\n  | \"😡\"\n  | \"😠\"\n  | \"🤬\"\n  | \"😈\"\n  | \"👿\"\n  | \"💀\"\n  | \"☠️\"\n  | \"☠\"\n  | \"💩\"\n  | \"🤡\"\n  | \"👹\"\n  | \"👺\"\n  | \"👻\"\n  | \"👽\"\n  | \"👾\"\n  | \"🤖\"\n  | \"😺\"\n  | \"😸\"\n  | \"😹\"\n  | \"😻\"\n  | \"😼\"\n  | \"😽\"\n  | \"🙀\"\n  | \"😿\"\n  | \"😾\"\n  | \"🙈\"\n  | \"🙉\"\n  | \"🙊\"\n  | \"💋\"\n  | \"💌\"\n  | \"💘\"\n  | \"💝\"\n  | \"💖\"\n  | \"💗\"\n  | \"💓\"\n  | \"💞\"\n  | \"💕\"\n  | \"💟\"\n  | \"❣️\"\n  | \"❣\"\n  | \"💔\"\n  | \"❤️‍🔥\"\n  | \"❤‍🔥\"\n  | \"❤️‍🩹\"\n  | \"❤‍🩹\"\n  | \"❤️\"\n  | \"❤\"\n  | \"🧡\"\n  | \"💛\"\n  | \"💚\"\n  | \"💙\"\n  | \"💜\"\n  | \"🤎\"\n  | \"🖤\"\n  | \"🤍\"\n  | \"💯\"\n  | \"💢\"\n  | \"💥\"\n  | \"💫\"\n  | \"💦\"\n  | \"💨\"\n  | \"🕳️\"\n  | \"🕳\"\n  | \"💣\"\n  | \"💬\"\n  | \"👁️‍🗨️\"\n  | \"🗨️\"\n  | \"🗨\"\n  | \"🗯️\"\n  | \"🗯\"\n  | \"💭\"\n  | \"💤\"\n  | \"👋🏻\"\n  | \"👋🏼\"\n  | \"👋🏽\"\n  | \"👋🏾\"\n  | \"👋🏿\"\n  | \"👋\"\n  | \"🤚🏻\"\n  | \"🤚🏼\"\n  | \"🤚🏽\"\n  | \"🤚🏾\"\n  | \"🤚🏿\"\n  | \"🤚\"\n  | \"🖐🏻\"\n  | \"🖐🏼\"\n  | \"🖐🏽\"\n  | \"🖐🏾\"\n  | \"🖐🏿\"\n  | \"🖐️\"\n  | \"🖐\"\n  | \"✋🏻\"\n  | \"✋🏼\"\n  | \"✋🏽\"\n  | \"✋🏾\"\n  | \"✋🏿\"\n  | \"✋\"\n  | \"🖖🏻\"\n  | \"🖖🏼\"\n  | \"🖖🏽\"\n  | \"🖖🏾\"\n  | \"🖖🏿\"\n  | \"🖖\"\n  | \"🫱🏻\"\n  | \"🫱🏼\"\n  | \"🫱🏽\"\n  | \"🫱🏾\"\n  | \"🫱🏿\"\n  | \"🫱\"\n  | \"🫲🏻\"\n  | \"🫲🏼\"\n  | \"🫲🏽\"\n  | \"🫲🏾\"\n  | \"🫲🏿\"\n  | \"🫲\"\n  | \"🫳🏻\"\n  | \"🫳🏼\"\n  | \"🫳🏽\"\n  | \"🫳🏾\"\n  | \"🫳🏿\"\n  | \"🫳\"\n  | \"🫴🏻\"\n  | \"🫴🏼\"\n  | \"🫴🏽\"\n  | \"🫴🏾\"\n  | \"🫴🏿\"\n  | \"🫴\"\n  | \"👌🏻\"\n  | \"👌🏼\"\n  | \"👌🏽\"\n  | \"👌🏾\"\n  | \"👌🏿\"\n  | \"👌\"\n  | \"🤌🏻\"\n  | \"🤌🏼\"\n  | \"🤌🏽\"\n  | \"🤌🏾\"\n  | \"🤌🏿\"\n  | \"🤌\"\n  | \"🤏🏻\"\n  | \"🤏🏼\"\n  | \"🤏🏽\"\n  | \"🤏🏾\"\n  | \"🤏🏿\"\n  | \"🤏\"\n  | \"✌🏻\"\n  | \"✌🏼\"\n  | \"✌🏽\"\n  | \"✌🏾\"\n  | \"✌🏿\"\n  | \"✌️\"\n  | \"✌\"\n  | \"🤞🏻\"\n  | \"🤞🏼\"\n  | \"🤞🏽\"\n  | \"🤞🏾\"\n  | \"🤞🏿\"\n  | \"🤞\"\n  | \"🫰🏻\"\n  | \"🫰🏼\"\n  | \"🫰🏽\"\n  | \"🫰🏾\"\n  | \"🫰🏿\"\n  | \"🫰\"\n  | \"🤟🏻\"\n  | \"🤟🏼\"\n  | \"🤟🏽\"\n  | \"🤟🏾\"\n  | \"🤟🏿\"\n  | \"🤟\"\n  | \"🤘🏻\"\n  | \"🤘🏼\"\n  | \"🤘🏽\"\n  | \"🤘🏾\"\n  | \"🤘🏿\"\n  | \"🤘\"\n  | \"🤙🏻\"\n  | \"🤙🏼\"\n  | \"🤙🏽\"\n  | \"🤙🏾\"\n  | \"🤙🏿\"\n  | \"🤙\"\n  | \"👈🏻\"\n  | \"👈🏼\"\n  | \"👈🏽\"\n  | \"👈🏾\"\n  | \"👈🏿\"\n  | \"👈\"\n  | \"👉🏻\"\n  | \"👉🏼\"\n  | \"👉🏽\"\n  | \"👉🏾\"\n  | \"👉🏿\"\n  | \"👉\"\n  | \"👆🏻\"\n  | \"👆🏼\"\n  | \"👆🏽\"\n  | \"👆🏾\"\n  | \"👆🏿\"\n  | \"👆\"\n  | \"🖕🏻\"\n  | \"🖕🏼\"\n  | \"🖕🏽\"\n  | \"🖕🏾\"\n  | \"🖕🏿\"\n  | \"🖕\"\n  | \"👇🏻\"\n  | \"👇🏼\"\n  | \"👇🏽\"\n  | \"👇🏾\"\n  | \"👇🏿\"\n  | \"👇\"\n  | \"☝🏻\"\n  | \"☝🏼\"\n  | \"☝🏽\"\n  | \"☝🏾\"\n  | \"☝🏿\"\n  | \"☝️\"\n  | \"☝\"\n  | \"🫵🏻\"\n  | \"🫵🏼\"\n  | \"🫵🏽\"\n  | \"🫵🏾\"\n  | \"🫵🏿\"\n  | \"🫵\"\n  | \"👍🏻\"\n  | \"👍🏼\"\n  | \"👍🏽\"\n  | \"👍🏾\"\n  | \"👍🏿\"\n  | \"👍\"\n  | \"👎🏻\"\n  | \"👎🏼\"\n  | \"👎🏽\"\n  | \"👎🏾\"\n  | \"👎🏿\"\n  | \"👎\"\n  | \"✊🏻\"\n  | \"✊🏼\"\n  | \"✊🏽\"\n  | \"✊🏾\"\n  | \"✊🏿\"\n  | \"✊\"\n  | \"👊🏻\"\n  | \"👊🏼\"\n  | \"👊🏽\"\n  | \"👊🏾\"\n  | \"👊🏿\"\n  | \"👊\"\n  | \"🤛🏻\"\n  | \"🤛🏼\"\n  | \"🤛🏽\"\n  | \"🤛🏾\"\n  | \"🤛🏿\"\n  | \"🤛\"\n  | \"🤜🏻\"\n  | \"🤜🏼\"\n  | \"🤜🏽\"\n  | \"🤜🏾\"\n  | \"🤜🏿\"\n  | \"🤜\"\n  | \"👏🏻\"\n  | \"👏🏼\"\n  | \"👏🏽\"\n  | \"👏🏾\"\n  | \"👏🏿\"\n  | \"👏\"\n  | \"🙌🏻\"\n  | \"🙌🏼\"\n  | \"🙌🏽\"\n  | \"🙌🏾\"\n  | \"🙌🏿\"\n  | \"🙌\"\n  | \"🫶🏻\"\n  | \"🫶🏼\"\n  | \"🫶🏽\"\n  | \"🫶🏾\"\n  | \"🫶🏿\"\n  | \"🫶\"\n  | \"👐🏻\"\n  | \"👐🏼\"\n  | \"👐🏽\"\n  | \"👐🏾\"\n  | \"👐🏿\"\n  | \"👐\"\n  | \"🤲🏻\"\n  | \"🤲🏼\"\n  | \"🤲🏽\"\n  | \"🤲🏾\"\n  | \"🤲🏿\"\n  | \"🤲\"\n  | \"🤝🏻\"\n  | \"🤝🏼\"\n  | \"🤝🏽\"\n  | \"🤝🏾\"\n  | \"🤝🏿\"\n  | \"🫱🏻‍🫲🏼\"\n  | \"🫱🏻‍🫲🏽\"\n  | \"🫱🏻‍🫲🏾\"\n  | \"🫱🏻‍🫲🏿\"\n  | \"🫱🏼‍🫲🏻\"\n  | \"🫱🏼‍🫲🏽\"\n  | \"🫱🏼‍🫲🏾\"\n  | \"🫱🏼‍🫲🏿\"\n  | \"🫱🏽‍🫲🏻\"\n  | \"🫱🏽‍🫲🏼\"\n  | \"🫱🏽‍🫲🏾\"\n  | \"🫱🏽‍🫲🏿\"\n  | \"🫱🏾‍🫲🏻\"\n  | \"🫱🏾‍🫲🏼\"\n  | \"🫱🏾‍🫲🏽\"\n  | \"🫱🏾‍🫲🏿\"\n  | \"🫱🏿‍🫲🏻\"\n  | \"🫱🏿‍🫲🏼\"\n  | \"🫱🏿‍🫲🏽\"\n  | \"🫱🏿‍🫲🏾\"\n  | \"🤝\"\n  | \"🙏🏻\"\n  | \"🙏🏼\"\n  | \"🙏🏽\"\n  | \"🙏🏾\"\n  | \"🙏🏿\"\n  | \"🙏\"\n  | \"✍🏻\"\n  | \"✍🏼\"\n  | \"✍🏽\"\n  | \"✍🏾\"\n  | \"✍🏿\"\n  | \"✍️\"\n  | \"✍\"\n  | \"💅🏻\"\n  | \"💅🏼\"\n  | \"💅🏽\"\n  | \"💅🏾\"\n  | \"💅🏿\"\n  | \"💅\"\n  | \"🤳🏻\"\n  | \"🤳🏼\"\n  | \"🤳🏽\"\n  | \"🤳🏾\"\n  | \"🤳🏿\"\n  | \"🤳\"\n  | \"💪🏻\"\n  | \"💪🏼\"\n  | \"💪🏽\"\n  | \"💪🏾\"\n  | \"💪🏿\"\n  | \"💪\"\n  | \"🦾\"\n  | \"🦿\"\n  | \"🦵🏻\"\n  | \"🦵🏼\"\n  | \"🦵🏽\"\n  | \"🦵🏾\"\n  | \"🦵🏿\"\n  | \"🦵\"\n  | \"🦶🏻\"\n  | \"🦶🏼\"\n  | \"🦶🏽\"\n  | \"🦶🏾\"\n  | \"🦶🏿\"\n  | \"🦶\"\n  | \"👂🏻\"\n  | \"👂🏼\"\n  | \"👂🏽\"\n  | \"👂🏾\"\n  | \"👂🏿\"\n  | \"👂\"\n  | \"🦻🏻\"\n  | \"🦻🏼\"\n  | \"🦻🏽\"\n  | \"🦻🏾\"\n  | \"🦻🏿\"\n  | \"🦻\"\n  | \"👃🏻\"\n  | \"👃🏼\"\n  | \"👃🏽\"\n  | \"👃🏾\"\n  | \"👃🏿\"\n  | \"👃\"\n  | \"🧠\"\n  | \"🫀\"\n  | \"🫁\"\n  | \"🦷\"\n  | \"🦴\"\n  | \"👀\"\n  | \"👁️\"\n  | \"👁\"\n  | \"👅\"\n  | \"👄\"\n  | \"🫦\"\n  | \"👶🏻\"\n  | \"👶🏼\"\n  | \"👶🏽\"\n  | \"👶🏾\"\n  | \"👶🏿\"\n  | \"👶\"\n  | \"🧒🏻\"\n  | \"🧒🏼\"\n  | \"🧒🏽\"\n  | \"🧒🏾\"\n  | \"🧒🏿\"\n  | \"🧒\"\n  | \"👦🏻\"\n  | \"👦🏼\"\n  | \"👦🏽\"\n  | \"👦🏾\"\n  | \"👦🏿\"\n  | \"👦\"\n  | \"👧🏻\"\n  | \"👧🏼\"\n  | \"👧🏽\"\n  | \"👧🏾\"\n  | \"👧🏿\"\n  | \"👧\"\n  | \"🧑🏻\"\n  | \"🧑🏼\"\n  | \"🧑🏽\"\n  | \"🧑🏾\"\n  | \"🧑🏿\"\n  | \"🧑\"\n  | \"👱🏻\"\n  | \"👱🏼\"\n  | \"👱🏽\"\n  | \"👱🏾\"\n  | \"👱🏿\"\n  | \"👱\"\n  | \"👨🏻\"\n  | \"👨🏼\"\n  | \"👨🏽\"\n  | \"👨🏾\"\n  | \"👨🏿\"\n  | \"👨\"\n  | \"🧔🏻\"\n  | \"🧔🏼\"\n  | \"🧔🏽\"\n  | \"🧔🏾\"\n  | \"🧔🏿\"\n  | \"🧔\"\n  | \"🧔🏻‍♂️\"\n  | \"🧔🏼‍♂️\"\n  | \"🧔🏽‍♂️\"\n  | \"🧔🏾‍♂️\"\n  | \"🧔🏿‍♂️\"\n  | \"🧔‍♂️\"\n  | \"🧔‍♂\"\n  | \"🧔🏻‍♀️\"\n  | \"🧔🏼‍♀️\"\n  | \"🧔🏽‍♀️\"\n  | \"🧔🏾‍♀️\"\n  | \"🧔🏿‍♀️\"\n  | \"🧔‍♀️\"\n  | \"🧔‍♀\"\n  | \"👨🏻‍🦰\"\n  | \"👨🏼‍🦰\"\n  | \"👨🏽‍🦰\"\n  | \"👨🏾‍🦰\"\n  | \"👨🏿‍🦰\"\n  | \"👨‍🦰\"\n  | \"👨🏻‍🦱\"\n  | \"👨🏼‍🦱\"\n  | \"👨🏽‍🦱\"\n  | \"👨🏾‍🦱\"\n  | \"👨🏿‍🦱\"\n  | \"👨‍🦱\"\n  | \"👨🏻‍🦳\"\n  | \"👨🏼‍🦳\"\n  | \"👨🏽‍🦳\"\n  | \"👨🏾‍🦳\"\n  | \"👨🏿‍🦳\"\n  | \"👨‍🦳\"\n  | \"👨🏻‍🦲\"\n  | \"👨🏼‍🦲\"\n  | \"👨🏽‍🦲\"\n  | \"👨🏾‍🦲\"\n  | \"👨🏿‍🦲\"\n  | \"👨‍🦲\"\n  | \"👩🏻\"\n  | \"👩🏼\"\n  | \"👩🏽\"\n  | \"👩🏾\"\n  | \"👩🏿\"\n  | \"👩\"\n  | \"👩🏻‍🦰\"\n  | \"👩🏼‍🦰\"\n  | \"👩🏽‍🦰\"\n  | \"👩🏾‍🦰\"\n  | \"👩🏿‍🦰\"\n  | \"👩‍🦰\"\n  | \"🧑🏻‍🦰\"\n  | \"🧑🏼‍🦰\"\n  | \"🧑🏽‍🦰\"\n  | \"🧑🏾‍🦰\"\n  | \"🧑🏿‍🦰\"\n  | \"🧑‍🦰\"\n  | \"👩🏻‍🦱\"\n  | \"👩🏼‍🦱\"\n  | \"👩🏽‍🦱\"\n  | \"👩🏾‍🦱\"\n  | \"👩🏿‍🦱\"\n  | \"👩‍🦱\"\n  | \"🧑🏻‍🦱\"\n  | \"🧑🏼‍🦱\"\n  | \"🧑🏽‍🦱\"\n  | \"🧑🏾‍🦱\"\n  | \"🧑🏿‍🦱\"\n  | \"🧑‍🦱\"\n  | \"👩🏻‍🦳\"\n  | \"👩🏼‍🦳\"\n  | \"👩🏽‍🦳\"\n  | \"👩🏾‍🦳\"\n  | \"👩🏿‍🦳\"\n  | \"👩‍🦳\"\n  | \"🧑🏻‍🦳\"\n  | \"🧑🏼‍🦳\"\n  | \"🧑🏽‍🦳\"\n  | \"🧑🏾‍🦳\"\n  | \"🧑🏿‍🦳\"\n  | \"🧑‍🦳\"\n  | \"👩🏻‍🦲\"\n  | \"👩🏼‍🦲\"\n  | \"👩🏽‍🦲\"\n  | \"👩🏾‍🦲\"\n  | \"👩🏿‍🦲\"\n  | \"👩‍🦲\"\n  | \"🧑🏻‍🦲\"\n  | \"🧑🏼‍🦲\"\n  | \"🧑🏽‍🦲\"\n  | \"🧑🏾‍🦲\"\n  | \"🧑🏿‍🦲\"\n  | \"🧑‍🦲\"\n  | \"👱🏻‍♀️\"\n  | \"👱🏼‍♀️\"\n  | \"👱🏽‍♀️\"\n  | \"👱🏾‍♀️\"\n  | \"👱🏿‍♀️\"\n  | \"👱‍♀️\"\n  | \"👱‍♀\"\n  | \"👱🏻‍♂️\"\n  | \"👱🏼‍♂️\"\n  | \"👱🏽‍♂️\"\n  | \"👱🏾‍♂️\"\n  | \"👱🏿‍♂️\"\n  | \"👱‍♂️\"\n  | \"👱‍♂\"\n  | \"🧓🏻\"\n  | \"🧓🏼\"\n  | \"🧓🏽\"\n  | \"🧓🏾\"\n  | \"🧓🏿\"\n  | \"🧓\"\n  | \"👴🏻\"\n  | \"👴🏼\"\n  | \"👴🏽\"\n  | \"👴🏾\"\n  | \"👴🏿\"\n  | \"👴\"\n  | \"👵🏻\"\n  | \"👵🏼\"\n  | \"👵🏽\"\n  | \"👵🏾\"\n  | \"👵🏿\"\n  | \"👵\"\n  | \"🙍🏻\"\n  | \"🙍🏼\"\n  | \"🙍🏽\"\n  | \"🙍🏾\"\n  | \"🙍🏿\"\n  | \"🙍\"\n  | \"🙍🏻‍♂️\"\n  | \"🙍🏼‍♂️\"\n  | \"🙍🏽‍♂️\"\n  | \"🙍🏾‍♂️\"\n  | \"🙍🏿‍♂️\"\n  | \"🙍‍♂️\"\n  | \"🙍‍♂\"\n  | \"🙍🏻‍♀️\"\n  | \"🙍🏼‍♀️\"\n  | \"🙍🏽‍♀️\"\n  | \"🙍🏾‍♀️\"\n  | \"🙍🏿‍♀️\"\n  | \"🙍‍♀️\"\n  | \"🙍‍♀\"\n  | \"🙎🏻\"\n  | \"🙎🏼\"\n  | \"🙎🏽\"\n  | \"🙎🏾\"\n  | \"🙎🏿\"\n  | \"🙎\"\n  | \"🙎🏻‍♂️\"\n  | \"🙎🏼‍♂️\"\n  | \"🙎🏽‍♂️\"\n  | \"🙎🏾‍♂️\"\n  | \"🙎🏿‍♂️\"\n  | \"🙎‍♂️\"\n  | \"🙎‍♂\"\n  | \"🙎🏻‍♀️\"\n  | \"🙎🏼‍♀️\"\n  | \"🙎🏽‍♀️\"\n  | \"🙎🏾‍♀️\"\n  | \"🙎🏿‍♀️\"\n  | \"🙎‍♀️\"\n  | \"🙎‍♀\"\n  | \"🙅🏻\"\n  | \"🙅🏼\"\n  | \"🙅🏽\"\n  | \"🙅🏾\"\n  | \"🙅🏿\"\n  | \"🙅\"\n  | \"🙅🏻‍♂️\"\n  | \"🙅🏼‍♂️\"\n  | \"🙅🏽‍♂️\"\n  | \"🙅🏾‍♂️\"\n  | \"🙅🏿‍♂️\"\n  | \"🙅‍♂️\"\n  | \"🙅‍♂\"\n  | \"🙅🏻‍♀️\"\n  | \"🙅🏼‍♀️\"\n  | \"🙅🏽‍♀️\"\n  | \"🙅🏾‍♀️\"\n  | \"🙅🏿‍♀️\"\n  | \"🙅‍♀️\"\n  | \"🙅‍♀\"\n  | \"🙆🏻\"\n  | \"🙆🏼\"\n  | \"🙆🏽\"\n  | \"🙆🏾\"\n  | \"🙆🏿\"\n  | \"🙆\"\n  | \"🙆🏻‍♂️\"\n  | \"🙆🏼‍♂️\"\n  | \"🙆🏽‍♂️\"\n  | \"🙆🏾‍♂️\"\n  | \"🙆🏿‍♂️\"\n  | \"🙆‍♂️\"\n  | \"🙆‍♂\"\n  | \"🙆🏻‍♀️\"\n  | \"🙆🏼‍♀️\"\n  | \"🙆🏽‍♀️\"\n  | \"🙆🏾‍♀️\"\n  | \"🙆🏿‍♀️\"\n  | \"🙆‍♀️\"\n  | \"🙆‍♀\"\n  | \"💁🏻\"\n  | \"💁🏼\"\n  | \"💁🏽\"\n  | \"💁🏾\"\n  | \"💁🏿\"\n  | \"💁\"\n  | \"💁🏻‍♂️\"\n  | \"💁🏼‍♂️\"\n  | \"💁🏽‍♂️\"\n  | \"💁🏾‍♂️\"\n  | \"💁🏿‍♂️\"\n  | \"💁‍♂️\"\n  | \"💁‍♂\"\n  | \"💁🏻‍♀️\"\n  | \"💁🏼‍♀️\"\n  | \"💁🏽‍♀️\"\n  | \"💁🏾‍♀️\"\n  | \"💁🏿‍♀️\"\n  | \"💁‍♀️\"\n  | \"💁‍♀\"\n  | \"🙋🏻\"\n  | \"🙋🏼\"\n  | \"🙋🏽\"\n  | \"🙋🏾\"\n  | \"🙋🏿\"\n  | \"🙋\"\n  | \"🙋🏻‍♂️\"\n  | \"🙋🏼‍♂️\"\n  | \"🙋🏽‍♂️\"\n  | \"🙋🏾‍♂️\"\n  | \"🙋🏿‍♂️\"\n  | \"🙋‍♂️\"\n  | \"🙋‍♂\"\n  | \"🙋🏻‍♀️\"\n  | \"🙋🏼‍♀️\"\n  | \"🙋🏽‍♀️\"\n  | \"🙋🏾‍♀️\"\n  | \"🙋🏿‍♀️\"\n  | \"🙋‍♀️\"\n  | \"🙋‍♀\"\n  | \"🧏🏻\"\n  | \"🧏🏼\"\n  | \"🧏🏽\"\n  | \"🧏🏾\"\n  | \"🧏🏿\"\n  | \"🧏\"\n  | \"🧏🏻‍♂️\"\n  | \"🧏🏼‍♂️\"\n  | \"🧏🏽‍♂️\"\n  | \"🧏🏾‍♂️\"\n  | \"🧏🏿‍♂️\"\n  | \"🧏‍♂️\"\n  | \"🧏‍♂\"\n  | \"🧏🏻‍♀️\"\n  | \"🧏🏼‍♀️\"\n  | \"🧏🏽‍♀️\"\n  | \"🧏🏾‍♀️\"\n  | \"🧏🏿‍♀️\"\n  | \"🧏‍♀️\"\n  | \"🧏‍♀\"\n  | \"🙇🏻\"\n  | \"🙇🏼\"\n  | \"🙇🏽\"\n  | \"🙇🏾\"\n  | \"🙇🏿\"\n  | \"🙇\"\n  | \"🙇🏻‍♂️\"\n  | \"🙇🏼‍♂️\"\n  | \"🙇🏽‍♂️\"\n  | \"🙇🏾‍♂️\"\n  | \"🙇🏿‍♂️\"\n  | \"🙇‍♂️\"\n  | \"🙇‍♂\"\n  | \"🙇🏻‍♀️\"\n  | \"🙇🏼‍♀️\"\n  | \"🙇🏽‍♀️\"\n  | \"🙇🏾‍♀️\"\n  | \"🙇🏿‍♀️\"\n  | \"🙇‍♀️\"\n  | \"🙇‍♀\"\n  | \"🤦🏻\"\n  | \"🤦🏼\"\n  | \"🤦🏽\"\n  | \"🤦🏾\"\n  | \"🤦🏿\"\n  | \"🤦\"\n  | \"🤦🏻‍♂️\"\n  | \"🤦🏼‍♂️\"\n  | \"🤦🏽‍♂️\"\n  | \"🤦🏾‍♂️\"\n  | \"🤦🏿‍♂️\"\n  | \"🤦‍♂️\"\n  | \"🤦‍♂\"\n  | \"🤦🏻‍♀️\"\n  | \"🤦🏼‍♀️\"\n  | \"🤦🏽‍♀️\"\n  | \"🤦🏾‍♀️\"\n  | \"🤦🏿‍♀️\"\n  | \"🤦‍♀️\"\n  | \"🤦‍♀\"\n  | \"🤷🏻\"\n  | \"🤷🏼\"\n  | \"🤷🏽\"\n  | \"🤷🏾\"\n  | \"🤷🏿\"\n  | \"🤷\"\n  | \"🤷🏻‍♂️\"\n  | \"🤷🏼‍♂️\"\n  | \"🤷🏽‍♂️\"\n  | \"🤷🏾‍♂️\"\n  | \"🤷🏿‍♂️\"\n  | \"🤷‍♂️\"\n  | \"🤷‍♂\"\n  | \"🤷🏻‍♀️\"\n  | \"🤷🏼‍♀️\"\n  | \"🤷🏽‍♀️\"\n  | \"🤷🏾‍♀️\"\n  | \"🤷🏿‍♀️\"\n  | \"🤷‍♀️\"\n  | \"🤷‍♀\"\n  | \"🧑🏻‍⚕️\"\n  | \"🧑🏼‍⚕️\"\n  | \"🧑🏽‍⚕️\"\n  | \"🧑🏾‍⚕️\"\n  | \"🧑🏿‍⚕️\"\n  | \"🧑‍⚕️\"\n  | \"🧑‍⚕\"\n  | \"👨🏻‍⚕️\"\n  | \"👨🏼‍⚕️\"\n  | \"👨🏽‍⚕️\"\n  | \"👨🏾‍⚕️\"\n  | \"👨🏿‍⚕️\"\n  | \"👨‍⚕️\"\n  | \"👨‍⚕\"\n  | \"👩🏻‍⚕️\"\n  | \"👩🏼‍⚕️\"\n  | \"👩🏽‍⚕️\"\n  | \"👩🏾‍⚕️\"\n  | \"👩🏿‍⚕️\"\n  | \"👩‍⚕️\"\n  | \"👩‍⚕\"\n  | \"🧑🏻‍🎓\"\n  | \"🧑🏼‍🎓\"\n  | \"🧑🏽‍🎓\"\n  | \"🧑🏾‍🎓\"\n  | \"🧑🏿‍🎓\"\n  | \"🧑‍🎓\"\n  | \"👨🏻‍🎓\"\n  | \"👨🏼‍🎓\"\n  | \"👨🏽‍🎓\"\n  | \"👨🏾‍🎓\"\n  | \"👨🏿‍🎓\"\n  | \"👨‍🎓\"\n  | \"👩🏻‍🎓\"\n  | \"👩🏼‍🎓\"\n  | \"👩🏽‍🎓\"\n  | \"👩🏾‍🎓\"\n  | \"👩🏿‍🎓\"\n  | \"👩‍🎓\"\n  | \"🧑🏻‍🏫\"\n  | \"🧑🏼‍🏫\"\n  | \"🧑🏽‍🏫\"\n  | \"🧑🏾‍🏫\"\n  | \"🧑🏿‍🏫\"\n  | \"🧑‍🏫\"\n  | \"👨🏻‍🏫\"\n  | \"👨🏼‍🏫\"\n  | \"👨🏽‍🏫\"\n  | \"👨🏾‍🏫\"\n  | \"👨🏿‍🏫\"\n  | \"👨‍🏫\"\n  | \"👩🏻‍🏫\"\n  | \"👩🏼‍🏫\"\n  | \"👩🏽‍🏫\"\n  | \"👩🏾‍🏫\"\n  | \"👩🏿‍🏫\"\n  | \"👩‍🏫\"\n  | \"🧑🏻‍⚖️\"\n  | \"🧑🏼‍⚖️\"\n  | \"🧑🏽‍⚖️\"\n  | \"🧑🏾‍⚖️\"\n  | \"🧑🏿‍⚖️\"\n  | \"🧑‍⚖️\"\n  | \"🧑‍⚖\"\n  | \"👨🏻‍⚖️\"\n  | \"👨🏼‍⚖️\"\n  | \"👨🏽‍⚖️\"\n  | \"👨🏾‍⚖️\"\n  | \"👨🏿‍⚖️\"\n  | \"👨‍⚖️\"\n  | \"👨‍⚖\"\n  | \"👩🏻‍⚖️\"\n  | \"👩🏼‍⚖️\"\n  | \"👩🏽‍⚖️\"\n  | \"👩🏾‍⚖️\"\n  | \"👩🏿‍⚖️\"\n  | \"👩‍⚖️\"\n  | \"👩‍⚖\"\n  | \"🧑🏻‍🌾\"\n  | \"🧑🏼‍🌾\"\n  | \"🧑🏽‍🌾\"\n  | \"🧑🏾‍🌾\"\n  | \"🧑🏿‍🌾\"\n  | \"🧑‍🌾\"\n  | \"👨🏻‍🌾\"\n  | \"👨🏼‍🌾\"\n  | \"👨🏽‍🌾\"\n  | \"👨🏾‍🌾\"\n  | \"👨🏿‍🌾\"\n  | \"👨‍🌾\"\n  | \"👩🏻‍🌾\"\n  | \"👩🏼‍🌾\"\n  | \"👩🏽‍🌾\"\n  | \"👩🏾‍🌾\"\n  | \"👩🏿‍🌾\"\n  | \"👩‍🌾\"\n  | \"🧑🏻‍🍳\"\n  | \"🧑🏼‍🍳\"\n  | \"🧑🏽‍🍳\"\n  | \"🧑🏾‍🍳\"\n  | \"🧑🏿‍🍳\"\n  | \"🧑‍🍳\"\n  | \"👨🏻‍🍳\"\n  | \"👨🏼‍🍳\"\n  | \"👨🏽‍🍳\"\n  | \"👨🏾‍🍳\"\n  | \"👨🏿‍🍳\"\n  | \"👨‍🍳\"\n  | \"👩🏻‍🍳\"\n  | \"👩🏼‍🍳\"\n  | \"👩🏽‍🍳\"\n  | \"👩🏾‍🍳\"\n  | \"👩🏿‍🍳\"\n  | \"👩‍🍳\"\n  | \"🧑🏻‍🔧\"\n  | \"🧑🏼‍🔧\"\n  | \"🧑🏽‍🔧\"\n  | \"🧑🏾‍🔧\"\n  | \"🧑🏿‍🔧\"\n  | \"🧑‍🔧\"\n  | \"👨🏻‍🔧\"\n  | \"👨🏼‍🔧\"\n  | \"👨🏽‍🔧\"\n  | \"👨🏾‍🔧\"\n  | \"👨🏿‍🔧\"\n  | \"👨‍🔧\"\n  | \"👩🏻‍🔧\"\n  | \"👩🏼‍🔧\"\n  | \"👩🏽‍🔧\"\n  | \"👩🏾‍🔧\"\n  | \"👩🏿‍🔧\"\n  | \"👩‍🔧\"\n  | \"🧑🏻‍🏭\"\n  | \"🧑🏼‍🏭\"\n  | \"🧑🏽‍🏭\"\n  | \"🧑🏾‍🏭\"\n  | \"🧑🏿‍🏭\"\n  | \"🧑‍🏭\"\n  | \"👨🏻‍🏭\"\n  | \"👨🏼‍🏭\"\n  | \"👨🏽‍🏭\"\n  | \"👨🏾‍🏭\"\n  | \"👨🏿‍🏭\"\n  | \"👨‍🏭\"\n  | \"👩🏻‍🏭\"\n  | \"👩🏼‍🏭\"\n  | \"👩🏽‍🏭\"\n  | \"👩🏾‍🏭\"\n  | \"👩🏿‍🏭\"\n  | \"👩‍🏭\"\n  | \"🧑🏻‍💼\"\n  | \"🧑🏼‍💼\"\n  | \"🧑🏽‍💼\"\n  | \"🧑🏾‍💼\"\n  | \"🧑🏿‍💼\"\n  | \"🧑‍💼\"\n  | \"👨🏻‍💼\"\n  | \"👨🏼‍💼\"\n  | \"👨🏽‍💼\"\n  | \"👨🏾‍💼\"\n  | \"👨🏿‍💼\"\n  | \"👨‍💼\"\n  | \"👩🏻‍💼\"\n  | \"👩🏼‍💼\"\n  | \"👩🏽‍💼\"\n  | \"👩🏾‍💼\"\n  | \"👩🏿‍💼\"\n  | \"👩‍💼\"\n  | \"🧑🏻‍🔬\"\n  | \"🧑🏼‍🔬\"\n  | \"🧑🏽‍🔬\"\n  | \"🧑🏾‍🔬\"\n  | \"🧑🏿‍🔬\"\n  | \"🧑‍🔬\"\n  | \"👨🏻‍🔬\"\n  | \"👨🏼‍🔬\"\n  | \"👨🏽‍🔬\"\n  | \"👨🏾‍🔬\"\n  | \"👨🏿‍🔬\"\n  | \"👨‍🔬\"\n  | \"👩🏻‍🔬\"\n  | \"👩🏼‍🔬\"\n  | \"👩🏽‍🔬\"\n  | \"👩🏾‍🔬\"\n  | \"👩🏿‍🔬\"\n  | \"👩‍🔬\"\n  | \"🧑🏻‍💻\"\n  | \"🧑🏼‍💻\"\n  | \"🧑🏽‍💻\"\n  | \"🧑🏾‍💻\"\n  | \"🧑🏿‍💻\"\n  | \"🧑‍💻\"\n  | \"👨🏻‍💻\"\n  | \"👨🏼‍💻\"\n  | \"👨🏽‍💻\"\n  | \"👨🏾‍💻\"\n  | \"👨🏿‍💻\"\n  | \"👨‍💻\"\n  | \"👩🏻‍💻\"\n  | \"👩🏼‍💻\"\n  | \"👩🏽‍💻\"\n  | \"👩🏾‍💻\"\n  | \"👩🏿‍💻\"\n  | \"👩‍💻\"\n  | \"🧑🏻‍🎤\"\n  | \"🧑🏼‍🎤\"\n  | \"🧑🏽‍🎤\"\n  | \"🧑🏾‍🎤\"\n  | \"🧑🏿‍🎤\"\n  | \"🧑‍🎤\"\n  | \"👨🏻‍🎤\"\n  | \"👨🏼‍🎤\"\n  | \"👨🏽‍🎤\"\n  | \"👨🏾‍🎤\"\n  | \"👨🏿‍🎤\"\n  | \"👨‍🎤\"\n  | \"👩🏻‍🎤\"\n  | \"👩🏼‍🎤\"\n  | \"👩🏽‍🎤\"\n  | \"👩🏾‍🎤\"\n  | \"👩🏿‍🎤\"\n  | \"👩‍🎤\"\n  | \"🧑🏻‍🎨\"\n  | \"🧑🏼‍🎨\"\n  | \"🧑🏽‍🎨\"\n  | \"🧑🏾‍🎨\"\n  | \"🧑🏿‍🎨\"\n  | \"🧑‍🎨\"\n  | \"👨🏻‍🎨\"\n  | \"👨🏼‍🎨\"\n  | \"👨🏽‍🎨\"\n  | \"👨🏾‍🎨\"\n  | \"👨🏿‍🎨\"\n  | \"👨‍🎨\"\n  | \"👩🏻‍🎨\"\n  | \"👩🏼‍🎨\"\n  | \"👩🏽‍🎨\"\n  | \"👩🏾‍🎨\"\n  | \"👩🏿‍🎨\"\n  | \"👩‍🎨\"\n  | \"🧑🏻‍✈️\"\n  | \"🧑🏼‍✈️\"\n  | \"🧑🏽‍✈️\"\n  | \"🧑🏾‍✈️\"\n  | \"🧑🏿‍✈️\"\n  | \"🧑‍✈️\"\n  | \"🧑‍✈\"\n  | \"👨🏻‍✈️\"\n  | \"👨🏼‍✈️\"\n  | \"👨🏽‍✈️\"\n  | \"👨🏾‍✈️\"\n  | \"👨🏿‍✈️\"\n  | \"👨‍✈️\"\n  | \"👨‍✈\"\n  | \"👩🏻‍✈️\"\n  | \"👩🏼‍✈️\"\n  | \"👩🏽‍✈️\"\n  | \"👩🏾‍✈️\"\n  | \"👩🏿‍✈️\"\n  | \"👩‍✈️\"\n  | \"👩‍✈\"\n  | \"🧑🏻‍🚀\"\n  | \"🧑🏼‍🚀\"\n  | \"🧑🏽‍🚀\"\n  | \"🧑🏾‍🚀\"\n  | \"🧑🏿‍🚀\"\n  | \"🧑‍🚀\"\n  | \"👨🏻‍🚀\"\n  | \"👨🏼‍🚀\"\n  | \"👨🏽‍🚀\"\n  | \"👨🏾‍🚀\"\n  | \"👨🏿‍🚀\"\n  | \"👨‍🚀\"\n  | \"👩🏻‍🚀\"\n  | \"👩🏼‍🚀\"\n  | \"👩🏽‍🚀\"\n  | \"👩🏾‍🚀\"\n  | \"👩🏿‍🚀\"\n  | \"👩‍🚀\"\n  | \"🧑🏻‍🚒\"\n  | \"🧑🏼‍🚒\"\n  | \"🧑🏽‍🚒\"\n  | \"🧑🏾‍🚒\"\n  | \"🧑🏿‍🚒\"\n  | \"🧑‍🚒\"\n  | \"👨🏻‍🚒\"\n  | \"👨🏼‍🚒\"\n  | \"👨🏽‍🚒\"\n  | \"👨🏾‍🚒\"\n  | \"👨🏿‍🚒\"\n  | \"👨‍🚒\"\n  | \"👩🏻‍🚒\"\n  | \"👩🏼‍🚒\"\n  | \"👩🏽‍🚒\"\n  | \"👩🏾‍🚒\"\n  | \"👩🏿‍🚒\"\n  | \"👩‍🚒\"\n  | \"👮🏻\"\n  | \"👮🏼\"\n  | \"👮🏽\"\n  | \"👮🏾\"\n  | \"👮🏿\"\n  | \"👮\"\n  | \"👮🏻‍♂️\"\n  | \"👮🏼‍♂️\"\n  | \"👮🏽‍♂️\"\n  | \"👮🏾‍♂️\"\n  | \"👮🏿‍♂️\"\n  | \"👮‍♂️\"\n  | \"👮‍♂\"\n  | \"👮🏻‍♀️\"\n  | \"👮🏼‍♀️\"\n  | \"👮🏽‍♀️\"\n  | \"👮🏾‍♀️\"\n  | \"👮🏿‍♀️\"\n  | \"👮‍♀️\"\n  | \"👮‍♀\"\n  | \"🕵🏻\"\n  | \"🕵🏼\"\n  | \"🕵🏽\"\n  | \"🕵🏾\"\n  | \"🕵🏿\"\n  | \"🕵️\"\n  | \"🕵\"\n  | \"🕵🏻‍♂️\"\n  | \"🕵🏼‍♂️\"\n  | \"🕵🏽‍♂️\"\n  | \"🕵🏾‍♂️\"\n  | \"🕵🏿‍♂️\"\n  | \"🕵️‍♂️\"\n  | \"🕵🏻‍♀️\"\n  | \"🕵🏼‍♀️\"\n  | \"🕵🏽‍♀️\"\n  | \"🕵🏾‍♀️\"\n  | \"🕵🏿‍♀️\"\n  | \"🕵️‍♀️\"\n  | \"💂🏻\"\n  | \"💂🏼\"\n  | \"💂🏽\"\n  | \"💂🏾\"\n  | \"💂🏿\"\n  | \"💂\"\n  | \"💂🏻‍♂️\"\n  | \"💂🏼‍♂️\"\n  | \"💂🏽‍♂️\"\n  | \"💂🏾‍♂️\"\n  | \"💂🏿‍♂️\"\n  | \"💂‍♂️\"\n  | \"💂‍♂\"\n  | \"💂🏻‍♀️\"\n  | \"💂🏼‍♀️\"\n  | \"💂🏽‍♀️\"\n  | \"💂🏾‍♀️\"\n  | \"💂🏿‍♀️\"\n  | \"💂‍♀️\"\n  | \"💂‍♀\"\n  | \"🥷🏻\"\n  | \"🥷🏼\"\n  | \"🥷🏽\"\n  | \"🥷🏾\"\n  | \"🥷🏿\"\n  | \"🥷\"\n  | \"👷🏻\"\n  | \"👷🏼\"\n  | \"👷🏽\"\n  | \"👷🏾\"\n  | \"👷🏿\"\n  | \"👷\"\n  | \"👷🏻‍♂️\"\n  | \"👷🏼‍♂️\"\n  | \"👷🏽‍♂️\"\n  | \"👷🏾‍♂️\"\n  | \"👷🏿‍♂️\"\n  | \"👷‍♂️\"\n  | \"👷‍♂\"\n  | \"👷🏻‍♀️\"\n  | \"👷🏼‍♀️\"\n  | \"👷🏽‍♀️\"\n  | \"👷🏾‍♀️\"\n  | \"👷🏿‍♀️\"\n  | \"👷‍♀️\"\n  | \"👷‍♀\"\n  | \"🫅🏻\"\n  | \"🫅🏼\"\n  | \"🫅🏽\"\n  | \"🫅🏾\"\n  | \"🫅🏿\"\n  | \"🫅\"\n  | \"🤴🏻\"\n  | \"🤴🏼\"\n  | \"🤴🏽\"\n  | \"🤴🏾\"\n  | \"🤴🏿\"\n  | \"🤴\"\n  | \"👸🏻\"\n  | \"👸🏼\"\n  | \"👸🏽\"\n  | \"👸🏾\"\n  | \"👸🏿\"\n  | \"👸\"\n  | \"👳🏻\"\n  | \"👳🏼\"\n  | \"👳🏽\"\n  | \"👳🏾\"\n  | \"👳🏿\"\n  | \"👳\"\n  | \"👳🏻‍♂️\"\n  | \"👳🏼‍♂️\"\n  | \"👳🏽‍♂️\"\n  | \"👳🏾‍♂️\"\n  | \"👳🏿‍♂️\"\n  | \"👳‍♂️\"\n  | \"👳‍♂\"\n  | \"👳🏻‍♀️\"\n  | \"👳🏼‍♀️\"\n  | \"👳🏽‍♀️\"\n  | \"👳🏾‍♀️\"\n  | \"👳🏿‍♀️\"\n  | \"👳‍♀️\"\n  | \"👳‍♀\"\n  | \"👲🏻\"\n  | \"👲🏼\"\n  | \"👲🏽\"\n  | \"👲🏾\"\n  | \"👲🏿\"\n  | \"👲\"\n  | \"🧕🏻\"\n  | \"🧕🏼\"\n  | \"🧕🏽\"\n  | \"🧕🏾\"\n  | \"🧕🏿\"\n  | \"🧕\"\n  | \"🤵🏻\"\n  | \"🤵🏼\"\n  | \"🤵🏽\"\n  | \"🤵🏾\"\n  | \"🤵🏿\"\n  | \"🤵\"\n  | \"🤵🏻‍♂️\"\n  | \"🤵🏼‍♂️\"\n  | \"🤵🏽‍♂️\"\n  | \"🤵🏾‍♂️\"\n  | \"🤵🏿‍♂️\"\n  | \"🤵‍♂️\"\n  | \"🤵‍♂\"\n  | \"🤵🏻‍♀️\"\n  | \"🤵🏼‍♀️\"\n  | \"🤵🏽‍♀️\"\n  | \"🤵🏾‍♀️\"\n  | \"🤵🏿‍♀️\"\n  | \"🤵‍♀️\"\n  | \"🤵‍♀\"\n  | \"👰🏻\"\n  | \"👰🏼\"\n  | \"👰🏽\"\n  | \"👰🏾\"\n  | \"👰🏿\"\n  | \"👰\"\n  | \"👰🏻‍♂️\"\n  | \"👰🏼‍♂️\"\n  | \"👰🏽‍♂️\"\n  | \"👰🏾‍♂️\"\n  | \"👰🏿‍♂️\"\n  | \"👰‍♂️\"\n  | \"👰‍♂\"\n  | \"👰🏻‍♀️\"\n  | \"👰🏼‍♀️\"\n  | \"👰🏽‍♀️\"\n  | \"👰🏾‍♀️\"\n  | \"👰🏿‍♀️\"\n  | \"👰‍♀️\"\n  | \"👰‍♀\"\n  | \"🤰🏻\"\n  | \"🤰🏼\"\n  | \"🤰🏽\"\n  | \"🤰🏾\"\n  | \"🤰🏿\"\n  | \"🤰\"\n  | \"🫃🏻\"\n  | \"🫃🏼\"\n  | \"🫃🏽\"\n  | \"🫃🏾\"\n  | \"🫃🏿\"\n  | \"🫃\"\n  | \"🫄🏻\"\n  | \"🫄🏼\"\n  | \"🫄🏽\"\n  | \"🫄🏾\"\n  | \"🫄🏿\"\n  | \"🫄\"\n  | \"🤱🏻\"\n  | \"🤱🏼\"\n  | \"🤱🏽\"\n  | \"🤱🏾\"\n  | \"🤱🏿\"\n  | \"🤱\"\n  | \"👩🏻‍🍼\"\n  | \"👩🏼‍🍼\"\n  | \"👩🏽‍🍼\"\n  | \"👩🏾‍🍼\"\n  | \"👩🏿‍🍼\"\n  | \"👩‍🍼\"\n  | \"👨🏻‍🍼\"\n  | \"👨🏼‍🍼\"\n  | \"👨🏽‍🍼\"\n  | \"👨🏾‍🍼\"\n  | \"👨🏿‍🍼\"\n  | \"👨‍🍼\"\n  | \"🧑🏻‍🍼\"\n  | \"🧑🏼‍🍼\"\n  | \"🧑🏽‍🍼\"\n  | \"🧑🏾‍🍼\"\n  | \"🧑🏿‍🍼\"\n  | \"🧑‍🍼\"\n  | \"👼🏻\"\n  | \"👼🏼\"\n  | \"👼🏽\"\n  | \"👼🏾\"\n  | \"👼🏿\"\n  | \"👼\"\n  | \"🎅🏻\"\n  | \"🎅🏼\"\n  | \"🎅🏽\"\n  | \"🎅🏾\"\n  | \"🎅🏿\"\n  | \"🎅\"\n  | \"🤶🏻\"\n  | \"🤶🏼\"\n  | \"🤶🏽\"\n  | \"🤶🏾\"\n  | \"🤶🏿\"\n  | \"🤶\"\n  | \"🧑🏻‍🎄\"\n  | \"🧑🏼‍🎄\"\n  | \"🧑🏽‍🎄\"\n  | \"🧑🏾‍🎄\"\n  | \"🧑🏿‍🎄\"\n  | \"🧑‍🎄\"\n  | \"🦸🏻\"\n  | \"🦸🏼\"\n  | \"🦸🏽\"\n  | \"🦸🏾\"\n  | \"🦸🏿\"\n  | \"🦸\"\n  | \"🦸🏻‍♂️\"\n  | \"🦸🏼‍♂️\"\n  | \"🦸🏽‍♂️\"\n  | \"🦸🏾‍♂️\"\n  | \"🦸🏿‍♂️\"\n  | \"🦸‍♂️\"\n  | \"🦸‍♂\"\n  | \"🦸🏻‍♀️\"\n  | \"🦸🏼‍♀️\"\n  | \"🦸🏽‍♀️\"\n  | \"🦸🏾‍♀️\"\n  | \"🦸🏿‍♀️\"\n  | \"🦸‍♀️\"\n  | \"🦸‍♀\"\n  | \"🦹🏻\"\n  | \"🦹🏼\"\n  | \"🦹🏽\"\n  | \"🦹🏾\"\n  | \"🦹🏿\"\n  | \"🦹\"\n  | \"🦹🏻‍♂️\"\n  | \"🦹🏼‍♂️\"\n  | \"🦹🏽‍♂️\"\n  | \"🦹🏾‍♂️\"\n  | \"🦹🏿‍♂️\"\n  | \"🦹‍♂️\"\n  | \"🦹‍♂\"\n  | \"🦹🏻‍♀️\"\n  | \"🦹🏼‍♀️\"\n  | \"🦹🏽‍♀️\"\n  | \"🦹🏾‍♀️\"\n  | \"🦹🏿‍♀️\"\n  | \"🦹‍♀️\"\n  | \"🦹‍♀\"\n  | \"🧙🏻\"\n  | \"🧙🏼\"\n  | \"🧙🏽\"\n  | \"🧙🏾\"\n  | \"🧙🏿\"\n  | \"🧙\"\n  | \"🧙🏻‍♂️\"\n  | \"🧙🏼‍♂️\"\n  | \"🧙🏽‍♂️\"\n  | \"🧙🏾‍♂️\"\n  | \"🧙🏿‍♂️\"\n  | \"🧙‍♂️\"\n  | \"🧙‍♂\"\n  | \"🧙🏻‍♀️\"\n  | \"🧙🏼‍♀️\"\n  | \"🧙🏽‍♀️\"\n  | \"🧙🏾‍♀️\"\n  | \"🧙🏿‍♀️\"\n  | \"🧙‍♀️\"\n  | \"🧙‍♀\"\n  | \"🧚🏻\"\n  | \"🧚🏼\"\n  | \"🧚🏽\"\n  | \"🧚🏾\"\n  | \"🧚🏿\"\n  | \"🧚\"\n  | \"🧚🏻‍♂️\"\n  | \"🧚🏼‍♂️\"\n  | \"🧚🏽‍♂️\"\n  | \"🧚🏾‍♂️\"\n  | \"🧚🏿‍♂️\"\n  | \"🧚‍♂️\"\n  | \"🧚‍♂\"\n  | \"🧚🏻‍♀️\"\n  | \"🧚🏼‍♀️\"\n  | \"🧚🏽‍♀️\"\n  | \"🧚🏾‍♀️\"\n  | \"🧚🏿‍♀️\"\n  | \"🧚‍♀️\"\n  | \"🧚‍♀\"\n  | \"🧛🏻\"\n  | \"🧛🏼\"\n  | \"🧛🏽\"\n  | \"🧛🏾\"\n  | \"🧛🏿\"\n  | \"🧛\"\n  | \"🧛🏻‍♂️\"\n  | \"🧛🏼‍♂️\"\n  | \"🧛🏽‍♂️\"\n  | \"🧛🏾‍♂️\"\n  | \"🧛🏿‍♂️\"\n  | \"🧛‍♂️\"\n  | \"🧛‍♂\"\n  | \"🧛🏻‍♀️\"\n  | \"🧛🏼‍♀️\"\n  | \"🧛🏽‍♀️\"\n  | \"🧛🏾‍♀️\"\n  | \"🧛🏿‍♀️\"\n  | \"🧛‍♀️\"\n  | \"🧛‍♀\"\n  | \"🧜🏻\"\n  | \"🧜🏼\"\n  | \"🧜🏽\"\n  | \"🧜🏾\"\n  | \"🧜🏿\"\n  | \"🧜\"\n  | \"🧜🏻‍♂️\"\n  | \"🧜🏼‍♂️\"\n  | \"🧜🏽‍♂️\"\n  | \"🧜🏾‍♂️\"\n  | \"🧜🏿‍♂️\"\n  | \"🧜‍♂️\"\n  | \"🧜‍♂\"\n  | \"🧜🏻‍♀️\"\n  | \"🧜🏼‍♀️\"\n  | \"🧜🏽‍♀️\"\n  | \"🧜🏾‍♀️\"\n  | \"🧜🏿‍♀️\"\n  | \"🧜‍♀️\"\n  | \"🧜‍♀\"\n  | \"🧝🏻\"\n  | \"🧝🏼\"\n  | \"🧝🏽\"\n  | \"🧝🏾\"\n  | \"🧝🏿\"\n  | \"🧝\"\n  | \"🧝🏻‍♂️\"\n  | \"🧝🏼‍♂️\"\n  | \"🧝🏽‍♂️\"\n  | \"🧝🏾‍♂️\"\n  | \"🧝🏿‍♂️\"\n  | \"🧝‍♂️\"\n  | \"🧝‍♂\"\n  | \"🧝🏻‍♀️\"\n  | \"🧝🏼‍♀️\"\n  | \"🧝🏽‍♀️\"\n  | \"🧝🏾‍♀️\"\n  | \"🧝🏿‍♀️\"\n  | \"🧝‍♀️\"\n  | \"🧝‍♀\"\n  | \"🧞\"\n  | \"🧞‍♂️\"\n  | \"🧞‍♂\"\n  | \"🧞‍♀️\"\n  | \"🧞‍♀\"\n  | \"🧟\"\n  | \"🧟‍♂️\"\n  | \"🧟‍♂\"\n  | \"🧟‍♀️\"\n  | \"🧟‍♀\"\n  | \"🧌\"\n  | \"💆🏻\"\n  | \"💆🏼\"\n  | \"💆🏽\"\n  | \"💆🏾\"\n  | \"💆🏿\"\n  | \"💆\"\n  | \"💆🏻‍♂️\"\n  | \"💆🏼‍♂️\"\n  | \"💆🏽‍♂️\"\n  | \"💆🏾‍♂️\"\n  | \"💆🏿‍♂️\"\n  | \"💆‍♂️\"\n  | \"💆‍♂\"\n  | \"💆🏻‍♀️\"\n  | \"💆🏼‍♀️\"\n  | \"💆🏽‍♀️\"\n  | \"💆🏾‍♀️\"\n  | \"💆🏿‍♀️\"\n  | \"💆‍♀️\"\n  | \"💆‍♀\"\n  | \"💇🏻\"\n  | \"💇🏼\"\n  | \"💇🏽\"\n  | \"💇🏾\"\n  | \"💇🏿\"\n  | \"💇\"\n  | \"💇🏻‍♂️\"\n  | \"💇🏼‍♂️\"\n  | \"💇🏽‍♂️\"\n  | \"💇🏾‍♂️\"\n  | \"💇🏿‍♂️\"\n  | \"💇‍♂️\"\n  | \"💇‍♂\"\n  | \"💇🏻‍♀️\"\n  | \"💇🏼‍♀️\"\n  | \"💇🏽‍♀️\"\n  | \"💇🏾‍♀️\"\n  | \"💇🏿‍♀️\"\n  | \"💇‍♀️\"\n  | \"💇‍♀\"\n  | \"🚶🏻\"\n  | \"🚶🏼\"\n  | \"🚶🏽\"\n  | \"🚶🏾\"\n  | \"🚶🏿\"\n  | \"🚶\"\n  | \"🚶🏻‍♂️\"\n  | \"🚶🏼‍♂️\"\n  | \"🚶🏽‍♂️\"\n  | \"🚶🏾‍♂️\"\n  | \"🚶🏿‍♂️\"\n  | \"🚶‍♂️\"\n  | \"🚶‍♂\"\n  | \"🚶🏻‍♀️\"\n  | \"🚶🏼‍♀️\"\n  | \"🚶🏽‍♀️\"\n  | \"🚶🏾‍♀️\"\n  | \"🚶🏿‍♀️\"\n  | \"🚶‍♀️\"\n  | \"🚶‍♀\"\n  | \"🧍🏻\"\n  | \"🧍🏼\"\n  | \"🧍🏽\"\n  | \"🧍🏾\"\n  | \"🧍🏿\"\n  | \"🧍\"\n  | \"🧍🏻‍♂️\"\n  | \"🧍🏼‍♂️\"\n  | \"🧍🏽‍♂️\"\n  | \"🧍🏾‍♂️\"\n  | \"🧍🏿‍♂️\"\n  | \"🧍‍♂️\"\n  | \"🧍‍♂\"\n  | \"🧍🏻‍♀️\"\n  | \"🧍🏼‍♀️\"\n  | \"🧍🏽‍♀️\"\n  | \"🧍🏾‍♀️\"\n  | \"🧍🏿‍♀️\"\n  | \"🧍‍♀️\"\n  | \"🧍‍♀\"\n  | \"🧎🏻\"\n  | \"🧎🏼\"\n  | \"🧎🏽\"\n  | \"🧎🏾\"\n  | \"🧎🏿\"\n  | \"🧎\"\n  | \"🧎🏻‍♂️\"\n  | \"🧎🏼‍♂️\"\n  | \"🧎🏽‍♂️\"\n  | \"🧎🏾‍♂️\"\n  | \"🧎🏿‍♂️\"\n  | \"🧎‍♂️\"\n  | \"🧎‍♂\"\n  | \"🧎🏻‍♀️\"\n  | \"🧎🏼‍♀️\"\n  | \"🧎🏽‍♀️\"\n  | \"🧎🏾‍♀️\"\n  | \"🧎🏿‍♀️\"\n  | \"🧎‍♀️\"\n  | \"🧎‍♀\"\n  | \"🧑🏻‍🦯\"\n  | \"🧑🏼‍🦯\"\n  | \"🧑🏽‍🦯\"\n  | \"🧑🏾‍🦯\"\n  | \"🧑🏿‍🦯\"\n  | \"🧑‍🦯\"\n  | \"👨🏻‍🦯\"\n  | \"👨🏼‍🦯\"\n  | \"👨🏽‍🦯\"\n  | \"👨🏾‍🦯\"\n  | \"👨🏿‍🦯\"\n  | \"👨‍🦯\"\n  | \"👩🏻‍🦯\"\n  | \"👩🏼‍🦯\"\n  | \"👩🏽‍🦯\"\n  | \"👩🏾‍🦯\"\n  | \"👩🏿‍🦯\"\n  | \"👩‍🦯\"\n  | \"🧑🏻‍🦼\"\n  | \"🧑🏼‍🦼\"\n  | \"🧑🏽‍🦼\"\n  | \"🧑🏾‍🦼\"\n  | \"🧑🏿‍🦼\"\n  | \"🧑‍🦼\"\n  | \"👨🏻‍🦼\"\n  | \"👨🏼‍🦼\"\n  | \"👨🏽‍🦼\"\n  | \"👨🏾‍🦼\"\n  | \"👨🏿‍🦼\"\n  | \"👨‍🦼\"\n  | \"👩🏻‍🦼\"\n  | \"👩🏼‍🦼\"\n  | \"👩🏽‍🦼\"\n  | \"👩🏾‍🦼\"\n  | \"👩🏿‍🦼\"\n  | \"👩‍🦼\"\n  | \"🧑🏻‍🦽\"\n  | \"🧑🏼‍🦽\"\n  | \"🧑🏽‍🦽\"\n  | \"🧑🏾‍🦽\"\n  | \"🧑🏿‍🦽\"\n  | \"🧑‍🦽\"\n  | \"👨🏻‍🦽\"\n  | \"👨🏼‍🦽\"\n  | \"👨🏽‍🦽\"\n  | \"👨🏾‍🦽\"\n  | \"👨🏿‍🦽\"\n  | \"👨‍🦽\"\n  | \"👩🏻‍🦽\"\n  | \"👩🏼‍🦽\"\n  | \"👩🏽‍🦽\"\n  | \"👩🏾‍🦽\"\n  | \"👩🏿‍🦽\"\n  | \"👩‍🦽\"\n  | \"🏃🏻\"\n  | \"🏃🏼\"\n  | \"🏃🏽\"\n  | \"🏃🏾\"\n  | \"🏃🏿\"\n  | \"🏃\"\n  | \"🏃🏻‍♂️\"\n  | \"🏃🏼‍♂️\"\n  | \"🏃🏽‍♂️\"\n  | \"🏃🏾‍♂️\"\n  | \"🏃🏿‍♂️\"\n  | \"🏃‍♂️\"\n  | \"🏃‍♂\"\n  | \"🏃🏻‍♀️\"\n  | \"🏃🏼‍♀️\"\n  | \"🏃🏽‍♀️\"\n  | \"🏃🏾‍♀️\"\n  | \"🏃🏿‍♀️\"\n  | \"🏃‍♀️\"\n  | \"🏃‍♀\"\n  | \"💃🏻\"\n  | \"💃🏼\"\n  | \"💃🏽\"\n  | \"💃🏾\"\n  | \"💃🏿\"\n  | \"💃\"\n  | \"🕺🏻\"\n  | \"🕺🏼\"\n  | \"🕺🏽\"\n  | \"🕺🏾\"\n  | \"🕺🏿\"\n  | \"🕺\"\n  | \"🕴🏻\"\n  | \"🕴🏼\"\n  | \"🕴🏽\"\n  | \"🕴🏾\"\n  | \"🕴🏿\"\n  | \"🕴️\"\n  | \"🕴\"\n  | \"👯\"\n  | \"👯‍♂️\"\n  | \"👯‍♂\"\n  | \"👯‍♀️\"\n  | \"👯‍♀\"\n  | \"🧖🏻\"\n  | \"🧖🏼\"\n  | \"🧖🏽\"\n  | \"🧖🏾\"\n  | \"🧖🏿\"\n  | \"🧖\"\n  | \"🧖🏻‍♂️\"\n  | \"🧖🏼‍♂️\"\n  | \"🧖🏽‍♂️\"\n  | \"🧖🏾‍♂️\"\n  | \"🧖🏿‍♂️\"\n  | \"🧖‍♂️\"\n  | \"🧖‍♂\"\n  | \"🧖🏻‍♀️\"\n  | \"🧖🏼‍♀️\"\n  | \"🧖🏽‍♀️\"\n  | \"🧖🏾‍♀️\"\n  | \"🧖🏿‍♀️\"\n  | \"🧖‍♀️\"\n  | \"🧖‍♀\"\n  | \"🧗🏻\"\n  | \"🧗🏼\"\n  | \"🧗🏽\"\n  | \"🧗🏾\"\n  | \"🧗🏿\"\n  | \"🧗\"\n  | \"🧗🏻‍♂️\"\n  | \"🧗🏼‍♂️\"\n  | \"🧗🏽‍♂️\"\n  | \"🧗🏾‍♂️\"\n  | \"🧗🏿‍♂️\"\n  | \"🧗‍♂️\"\n  | \"🧗‍♂\"\n  | \"🧗🏻‍♀️\"\n  | \"🧗🏼‍♀️\"\n  | \"🧗🏽‍♀️\"\n  | \"🧗🏾‍♀️\"\n  | \"🧗🏿‍♀️\"\n  | \"🧗‍♀️\"\n  | \"🧗‍♀\"\n  | \"🤺\"\n  | \"🏇🏻\"\n  | \"🏇🏼\"\n  | \"🏇🏽\"\n  | \"🏇🏾\"\n  | \"🏇🏿\"\n  | \"🏇\"\n  | \"⛷️\"\n  | \"⛷\"\n  | \"🏂🏻\"\n  | \"🏂🏼\"\n  | \"🏂🏽\"\n  | \"🏂🏾\"\n  | \"🏂🏿\"\n  | \"🏂\"\n  | \"🏌🏻\"\n  | \"🏌🏼\"\n  | \"🏌🏽\"\n  | \"🏌🏾\"\n  | \"🏌🏿\"\n  | \"🏌️\"\n  | \"🏌\"\n  | \"🏌🏻‍♂️\"\n  | \"🏌🏼‍♂️\"\n  | \"🏌🏽‍♂️\"\n  | \"🏌🏾‍♂️\"\n  | \"🏌🏿‍♂️\"\n  | \"🏌️‍♂️\"\n  | \"🏌🏻‍♀️\"\n  | \"🏌🏼‍♀️\"\n  | \"🏌🏽‍♀️\"\n  | \"🏌🏾‍♀️\"\n  | \"🏌🏿‍♀️\"\n  | \"🏌️‍♀️\"\n  | \"🏄🏻\"\n  | \"🏄🏼\"\n  | \"🏄🏽\"\n  | \"🏄🏾\"\n  | \"🏄🏿\"\n  | \"🏄\"\n  | \"🏄🏻‍♂️\"\n  | \"🏄🏼‍♂️\"\n  | \"🏄🏽‍♂️\"\n  | \"🏄🏾‍♂️\"\n  | \"🏄🏿‍♂️\"\n  | \"🏄‍♂️\"\n  | \"🏄‍♂\"\n  | \"🏄🏻‍♀️\"\n  | \"🏄🏼‍♀️\"\n  | \"🏄🏽‍♀️\"\n  | \"🏄🏾‍♀️\"\n  | \"🏄🏿‍♀️\"\n  | \"🏄‍♀️\"\n  | \"🏄‍♀\"\n  | \"🚣🏻\"\n  | \"🚣🏼\"\n  | \"🚣🏽\"\n  | \"🚣🏾\"\n  | \"🚣🏿\"\n  | \"🚣\"\n  | \"🚣🏻‍♂️\"\n  | \"🚣🏼‍♂️\"\n  | \"🚣🏽‍♂️\"\n  | \"🚣🏾‍♂️\"\n  | \"🚣🏿‍♂️\"\n  | \"🚣‍♂️\"\n  | \"🚣‍♂\"\n  | \"🚣🏻‍♀️\"\n  | \"🚣🏼‍♀️\"\n  | \"🚣🏽‍♀️\"\n  | \"🚣🏾‍♀️\"\n  | \"🚣🏿‍♀️\"\n  | \"🚣‍♀️\"\n  | \"🚣‍♀\"\n  | \"🏊🏻\"\n  | \"🏊🏼\"\n  | \"🏊🏽\"\n  | \"🏊🏾\"\n  | \"🏊🏿\"\n  | \"🏊\"\n  | \"🏊🏻‍♂️\"\n  | \"🏊🏼‍♂️\"\n  | \"🏊🏽‍♂️\"\n  | \"🏊🏾‍♂️\"\n  | \"🏊🏿‍♂️\"\n  | \"🏊‍♂️\"\n  | \"🏊‍♂\"\n  | \"🏊🏻‍♀️\"\n  | \"🏊🏼‍♀️\"\n  | \"🏊🏽‍♀️\"\n  | \"🏊🏾‍♀️\"\n  | \"🏊🏿‍♀️\"\n  | \"🏊‍♀️\"\n  | \"🏊‍♀\"\n  | \"⛹🏻\"\n  | \"⛹🏼\"\n  | \"⛹🏽\"\n  | \"⛹🏾\"\n  | \"⛹🏿\"\n  | \"⛹️\"\n  | \"⛹\"\n  | \"⛹🏻‍♂️\"\n  | \"⛹🏼‍♂️\"\n  | \"⛹🏽‍♂️\"\n  | \"⛹🏾‍♂️\"\n  | \"⛹🏿‍♂️\"\n  | \"⛹️‍♂️\"\n  | \"⛹🏻‍♀️\"\n  | \"⛹🏼‍♀️\"\n  | \"⛹🏽‍♀️\"\n  | \"⛹🏾‍♀️\"\n  | \"⛹🏿‍♀️\"\n  | \"⛹️‍♀️\"\n  | \"🏋🏻\"\n  | \"🏋🏼\"\n  | \"🏋🏽\"\n  | \"🏋🏾\"\n  | \"🏋🏿\"\n  | \"🏋️\"\n  | \"🏋\"\n  | \"🏋🏻‍♂️\"\n  | \"🏋🏼‍♂️\"\n  | \"🏋🏽‍♂️\"\n  | \"🏋🏾‍♂️\"\n  | \"🏋🏿‍♂️\"\n  | \"🏋️‍♂️\"\n  | \"🏋🏻‍♀️\"\n  | \"🏋🏼‍♀️\"\n  | \"🏋🏽‍♀️\"\n  | \"🏋🏾‍♀️\"\n  | \"🏋🏿‍♀️\"\n  | \"🏋️‍♀️\"\n  | \"🚴🏻\"\n  | \"🚴🏼\"\n  | \"🚴🏽\"\n  | \"🚴🏾\"\n  | \"🚴🏿\"\n  | \"🚴\"\n  | \"🚴🏻‍♂️\"\n  | \"🚴🏼‍♂️\"\n  | \"🚴🏽‍♂️\"\n  | \"🚴🏾‍♂️\"\n  | \"🚴🏿‍♂️\"\n  | \"🚴‍♂️\"\n  | \"🚴‍♂\"\n  | \"🚴🏻‍♀️\"\n  | \"🚴🏼‍♀️\"\n  | \"🚴🏽‍♀️\"\n  | \"🚴🏾‍♀️\"\n  | \"🚴🏿‍♀️\"\n  | \"🚴‍♀️\"\n  | \"🚴‍♀\"\n  | \"🚵🏻\"\n  | \"🚵🏼\"\n  | \"🚵🏽\"\n  | \"🚵🏾\"\n  | \"🚵🏿\"\n  | \"🚵\"\n  | \"🚵🏻‍♂️\"\n  | \"🚵🏼‍♂️\"\n  | \"🚵🏽‍♂️\"\n  | \"🚵🏾‍♂️\"\n  | \"🚵🏿‍♂️\"\n  | \"🚵‍♂️\"\n  | \"🚵‍♂\"\n  | \"🚵🏻‍♀️\"\n  | \"🚵🏼‍♀️\"\n  | \"🚵🏽‍♀️\"\n  | \"🚵🏾‍♀️\"\n  | \"🚵🏿‍♀️\"\n  | \"🚵‍♀️\"\n  | \"🚵‍♀\"\n  | \"🤸🏻\"\n  | \"🤸🏼\"\n  | \"🤸🏽\"\n  | \"🤸🏾\"\n  | \"🤸🏿\"\n  | \"🤸\"\n  | \"🤸🏻‍♂️\"\n  | \"🤸🏼‍♂️\"\n  | \"🤸🏽‍♂️\"\n  | \"🤸🏾‍♂️\"\n  | \"🤸🏿‍♂️\"\n  | \"🤸‍♂️\"\n  | \"🤸‍♂\"\n  | \"🤸🏻‍♀️\"\n  | \"🤸🏼‍♀️\"\n  | \"🤸🏽‍♀️\"\n  | \"🤸🏾‍♀️\"\n  | \"🤸🏿‍♀️\"\n  | \"🤸‍♀️\"\n  | \"🤸‍♀\"\n  | \"🤼\"\n  | \"🤼‍♂️\"\n  | \"🤼‍♂\"\n  | \"🤼‍♀️\"\n  | \"🤼‍♀\"\n  | \"🤽🏻\"\n  | \"🤽🏼\"\n  | \"🤽🏽\"\n  | \"🤽🏾\"\n  | \"🤽🏿\"\n  | \"🤽\"\n  | \"🤽🏻‍♂️\"\n  | \"🤽🏼‍♂️\"\n  | \"🤽🏽‍♂️\"\n  | \"🤽🏾‍♂️\"\n  | \"🤽🏿‍♂️\"\n  | \"🤽‍♂️\"\n  | \"🤽‍♂\"\n  | \"🤽🏻‍♀️\"\n  | \"🤽🏼‍♀️\"\n  | \"🤽🏽‍♀️\"\n  | \"🤽🏾‍♀️\"\n  | \"🤽🏿‍♀️\"\n  | \"🤽‍♀️\"\n  | \"🤽‍♀\"\n  | \"🤾🏻\"\n  | \"🤾🏼\"\n  | \"🤾🏽\"\n  | \"🤾🏾\"\n  | \"🤾🏿\"\n  | \"🤾\"\n  | \"🤾🏻‍♂️\"\n  | \"🤾🏼‍♂️\"\n  | \"🤾🏽‍♂️\"\n  | \"🤾🏾‍♂️\"\n  | \"🤾🏿‍♂️\"\n  | \"🤾‍♂️\"\n  | \"🤾‍♂\"\n  | \"🤾🏻‍♀️\"\n  | \"🤾🏼‍♀️\"\n  | \"🤾🏽‍♀️\"\n  | \"🤾🏾‍♀️\"\n  | \"🤾🏿‍♀️\"\n  | \"🤾‍♀️\"\n  | \"🤾‍♀\"\n  | \"🤹🏻\"\n  | \"🤹🏼\"\n  | \"🤹🏽\"\n  | \"🤹🏾\"\n  | \"🤹🏿\"\n  | \"🤹\"\n  | \"🤹🏻‍♂️\"\n  | \"🤹🏼‍♂️\"\n  | \"🤹🏽‍♂️\"\n  | \"🤹🏾‍♂️\"\n  | \"🤹🏿‍♂️\"\n  | \"🤹‍♂️\"\n  | \"🤹‍♂\"\n  | \"🤹🏻‍♀️\"\n  | \"🤹🏼‍♀️\"\n  | \"🤹🏽‍♀️\"\n  | \"🤹🏾‍♀️\"\n  | \"🤹🏿‍♀️\"\n  | \"🤹‍♀️\"\n  | \"🤹‍♀\"\n  | \"🧘🏻\"\n  | \"🧘🏼\"\n  | \"🧘🏽\"\n  | \"🧘🏾\"\n  | \"🧘🏿\"\n  | \"🧘\"\n  | \"🧘🏻‍♂️\"\n  | \"🧘🏼‍♂️\"\n  | \"🧘🏽‍♂️\"\n  | \"🧘🏾‍♂️\"\n  | \"🧘🏿‍♂️\"\n  | \"🧘‍♂️\"\n  | \"🧘‍♂\"\n  | \"🧘🏻‍♀️\"\n  | \"🧘🏼‍♀️\"\n  | \"🧘🏽‍♀️\"\n  | \"🧘🏾‍♀️\"\n  | \"🧘🏿‍♀️\"\n  | \"🧘‍♀️\"\n  | \"🧘‍♀\"\n  | \"🛀🏻\"\n  | \"🛀🏼\"\n  | \"🛀🏽\"\n  | \"🛀🏾\"\n  | \"🛀🏿\"\n  | \"🛀\"\n  | \"🛌🏻\"\n  | \"🛌🏼\"\n  | \"🛌🏽\"\n  | \"🛌🏾\"\n  | \"🛌🏿\"\n  | \"🛌\"\n  | \"🧑🏻‍🤝‍🧑🏻\"\n  | \"🧑🏻‍🤝‍🧑🏼\"\n  | \"🧑🏻‍🤝‍🧑🏽\"\n  | \"🧑🏻‍🤝‍🧑🏾\"\n  | \"🧑🏻‍🤝‍🧑🏿\"\n  | \"🧑🏼‍🤝‍🧑🏻\"\n  | \"🧑🏼‍🤝‍🧑🏼\"\n  | \"🧑🏼‍🤝‍🧑🏽\"\n  | \"🧑🏼‍🤝‍🧑🏾\"\n  | \"🧑🏼‍🤝‍🧑🏿\"\n  | \"🧑🏽‍🤝‍🧑🏻\"\n  | \"🧑🏽‍🤝‍🧑🏼\"\n  | \"🧑🏽‍🤝‍🧑🏽\"\n  | \"🧑🏽‍🤝‍🧑🏾\"\n  | \"🧑🏽‍🤝‍🧑🏿\"\n  | \"🧑🏾‍🤝‍🧑🏻\"\n  | \"🧑🏾‍🤝‍🧑🏼\"\n  | \"🧑🏾‍🤝‍🧑🏽\"\n  | \"🧑🏾‍🤝‍🧑🏾\"\n  | \"🧑🏾‍🤝‍🧑🏿\"\n  | \"🧑🏿‍🤝‍🧑🏻\"\n  | \"🧑🏿‍🤝‍🧑🏼\"\n  | \"🧑🏿‍🤝‍🧑🏽\"\n  | \"🧑🏿‍🤝‍🧑🏾\"\n  | \"🧑🏿‍🤝‍🧑🏿\"\n  | \"🧑‍🤝‍🧑\"\n  | \"👭\"\n  | \"👫\"\n  | \"👬\"\n  | \"💏\"\n  | \"💑\"\n  | \"👪\"\n  | \"👨‍👩‍👦\"\n  | \"👨‍👩‍👧\"\n  | \"👨‍👩‍👧‍👦\"\n  | \"👨‍👩‍👦‍👦\"\n  | \"👨‍👩‍👧‍👧\"\n  | \"👨‍👨‍👦\"\n  | \"👨‍👨‍👧\"\n  | \"👨‍👨‍👧‍👦\"\n  | \"👨‍👨‍👦‍👦\"\n  | \"👨‍👨‍👧‍👧\"\n  | \"👩‍👩‍👦\"\n  | \"👩‍👩‍👧\"\n  | \"👩‍👩‍👧‍👦\"\n  | \"👩‍👩‍👦‍👦\"\n  | \"👩‍👩‍👧‍👧\"\n  | \"👨‍👦\"\n  | \"👨‍👦‍👦\"\n  | \"👨‍👧\"\n  | \"👨‍👧‍👦\"\n  | \"👨‍👧‍👧\"\n  | \"👩‍👦\"\n  | \"👩‍👦‍👦\"\n  | \"👩‍👧\"\n  | \"👩‍👧‍👦\"\n  | \"👩‍👧‍👧\"\n  | \"🗣️\"\n  | \"🗣\"\n  | \"👤\"\n  | \"👥\"\n  | \"🫂\"\n  | \"👣\"\n  | \"🐵\"\n  | \"🐒\"\n  | \"🦍\"\n  | \"🦧\"\n  | \"🐶\"\n  | \"🐕\"\n  | \"🦮\"\n  | \"🐕‍🦺\"\n  | \"🐩\"\n  | \"🐺\"\n  | \"🦊\"\n  | \"🦝\"\n  | \"🐱\"\n  | \"🐈\"\n  | \"🐈‍⬛\"\n  | \"🦁\"\n  | \"🐯\"\n  | \"🐅\"\n  | \"🐆\"\n  | \"🐴\"\n  | \"🐎\"\n  | \"🦄\"\n  | \"🦓\"\n  | \"🦌\"\n  | \"🦬\"\n  | \"🐮\"\n  | \"🐂\"\n  | \"🐃\"\n  | \"🐄\"\n  | \"🐷\"\n  | \"🐖\"\n  | \"🐗\"\n  | \"🐽\"\n  | \"🐏\"\n  | \"🐑\"\n  | \"🐐\"\n  | \"🐪\"\n  | \"🐫\"\n  | \"🦙\"\n  | \"🦒\"\n  | \"🐘\"\n  | \"🦣\"\n  | \"🦏\"\n  | \"🦛\"\n  | \"🐭\"\n  | \"🐁\"\n  | \"🐀\"\n  | \"🐹\"\n  | \"🐰\"\n  | \"🐇\"\n  | \"🐿️\"\n  | \"🐿\"\n  | \"🦫\"\n  | \"🦔\"\n  | \"🦇\"\n  | \"🐻\"\n  | \"🐻‍❄️\"\n  | \"🐻‍❄\"\n  | \"🐨\"\n  | \"🐼\"\n  | \"🦥\"\n  | \"🦦\"\n  | \"🦨\"\n  | \"🦘\"\n  | \"🦡\"\n  | \"🐾\"\n  | \"🦃\"\n  | \"🐔\"\n  | \"🐓\"\n  | \"🐣\"\n  | \"🐤\"\n  | \"🐥\"\n  | \"🐦\"\n  | \"🐧\"\n  | \"🕊️\"\n  | \"🕊\"\n  | \"🦅\"\n  | \"🦆\"\n  | \"🦢\"\n  | \"🦉\"\n  | \"🦤\"\n  | \"🪶\"\n  | \"🦩\"\n  | \"🦚\"\n  | \"🦜\"\n  | \"🐸\"\n  | \"🐊\"\n  | \"🐢\"\n  | \"🦎\"\n  | \"🐍\"\n  | \"🐲\"\n  | \"🐉\"\n  | \"🦕\"\n  | \"🦖\"\n  | \"🐳\"\n  | \"🐋\"\n  | \"🐬\"\n  | \"🦭\"\n  | \"🐟\"\n  | \"🐠\"\n  | \"🐡\"\n  | \"🦈\"\n  | \"🐙\"\n  | \"🐚\"\n  | \"🪸\"\n  | \"🐌\"\n  | \"🦋\"\n  | \"🐛\"\n  | \"🐜\"\n  | \"🐝\"\n  | \"🪲\"\n  | \"🐞\"\n  | \"🦗\"\n  | \"🪳\"\n  | \"🕷️\"\n  | \"🕷\"\n  | \"🕸️\"\n  | \"🕸\"\n  | \"🦂\"\n  | \"🦟\"\n  | \"🪰\"\n  | \"🪱\"\n  | \"🦠\"\n  | \"💐\"\n  | \"🌸\"\n  | \"💮\"\n  | \"🪷\"\n  | \"🏵️\"\n  | \"🏵\"\n  | \"🌹\"\n  | \"🥀\"\n  | \"🌺\"\n  | \"🌻\"\n  | \"🌼\"\n  | \"🌷\"\n  | \"🌱\"\n  | \"🪴\"\n  | \"🌲\"\n  | \"🌳\"\n  | \"🌴\"\n  | \"🌵\"\n  | \"🌾\"\n  | \"🌿\"\n  | \"☘️\"\n  | \"☘\"\n  | \"🍀\"\n  | \"🍁\"\n  | \"🍂\"\n  | \"🍃\"\n  | \"🪹\"\n  | \"🪺\"\n  | \"🍇\"\n  | \"🍈\"\n  | \"🍉\"\n  | \"🍊\"\n  | \"🍋\"\n  | \"🍌\"\n  | \"🍍\"\n  | \"🥭\"\n  | \"🍎\"\n  | \"🍏\"\n  | \"🍐\"\n  | \"🍑\"\n  | \"🍒\"\n  | \"🍓\"\n  | \"🫐\"\n  | \"🥝\"\n  | \"🍅\"\n  | \"🫒\"\n  | \"🥥\"\n  | \"🥑\"\n  | \"🍆\"\n  | \"🥔\"\n  | \"🥕\"\n  | \"🌽\"\n  | \"🌶️\"\n  | \"🌶\"\n  | \"🫑\"\n  | \"🥒\"\n  | \"🥬\"\n  | \"🥦\"\n  | \"🧄\"\n  | \"🧅\"\n  | \"🍄\"\n  | \"🥜\"\n  | \"🫘\"\n  | \"🌰\"\n  | \"🍞\"\n  | \"🥐\"\n  | \"🥖\"\n  | \"🫓\"\n  | \"🥨\"\n  | \"🥯\"\n  | \"🥞\"\n  | \"🧇\"\n  | \"🧀\"\n  | \"🍖\"\n  | \"🍗\"\n  | \"🥩\"\n  | \"🥓\"\n  | \"🍔\"\n  | \"🍟\"\n  | \"🍕\"\n  | \"🌭\"\n  | \"🥪\"\n  | \"🌮\"\n  | \"🌯\"\n  | \"🫔\"\n  | \"🥙\"\n  | \"🧆\"\n  | \"🥚\"\n  | \"🍳\"\n  | \"🥘\"\n  | \"🍲\"\n  | \"🫕\"\n  | \"🥣\"\n  | \"🥗\"\n  | \"🍿\"\n  | \"🧈\"\n  | \"🧂\"\n  | \"🥫\"\n  | \"🍱\"\n  | \"🍘\"\n  | \"🍙\"\n  | \"🍚\"\n  | \"🍛\"\n  | \"🍜\"\n  | \"🍝\"\n  | \"🍠\"\n  | \"🍢\"\n  | \"🍣\"\n  | \"🍤\"\n  | \"🍥\"\n  | \"🥮\"\n  | \"🍡\"\n  | \"🥟\"\n  | \"🥠\"\n  | \"🥡\"\n  | \"🦀\"\n  | \"🦞\"\n  | \"🦐\"\n  | \"🦑\"\n  | \"🦪\"\n  | \"🍦\"\n  | \"🍧\"\n  | \"🍨\"\n  | \"🍩\"\n  | \"🍪\"\n  | \"🎂\"\n  | \"🍰\"\n  | \"🧁\"\n  | \"🥧\"\n  | \"🍫\"\n  | \"🍬\"\n  | \"🍭\"\n  | \"🍮\"\n  | \"🍯\"\n  | \"🍼\"\n  | \"🥛\"\n  | \"☕\"\n  | \"🫖\"\n  | \"🍵\"\n  | \"🍶\"\n  | \"🍾\"\n  | \"🍷\"\n  | \"🍸\"\n  | \"🍹\"\n  | \"🍺\"\n  | \"🍻\"\n  | \"🥂\"\n  | \"🥃\"\n  | \"🫗\"\n  | \"🥤\"\n  | \"🧋\"\n  | \"🧃\"\n  | \"🧉\"\n  | \"🧊\"\n  | \"🥢\"\n  | \"🍽️\"\n  | \"🍽\"\n  | \"🍴\"\n  | \"🥄\"\n  | \"🔪\"\n  | \"🫙\"\n  | \"🏺\"\n  | \"🌍\"\n  | \"🌎\"\n  | \"🌏\"\n  | \"🌐\"\n  | \"🗺️\"\n  | \"🗺\"\n  | \"🗾\"\n  | \"🧭\"\n  | \"🏔️\"\n  | \"🏔\"\n  | \"⛰️\"\n  | \"⛰\"\n  | \"🌋\"\n  | \"🗻\"\n  | \"🏕️\"\n  | \"🏕\"\n  | \"🏖️\"\n  | \"🏖\"\n  | \"🏜️\"\n  | \"🏜\"\n  | \"🏝️\"\n  | \"🏝\"\n  | \"🏞️\"\n  | \"🏞\"\n  | \"🏟️\"\n  | \"🏟\"\n  | \"🏛️\"\n  | \"🏛\"\n  | \"🏗️\"\n  | \"🏗\"\n  | \"🧱\"\n  | \"🪨\"\n  | \"🪵\"\n  | \"🛖\"\n  | \"🏘️\"\n  | \"🏘\"\n  | \"🏚️\"\n  | \"🏚\"\n  | \"🏠\"\n  | \"🏡\"\n  | \"🏢\"\n  | \"🏣\"\n  | \"🏤\"\n  | \"🏥\"\n  | \"🏦\"\n  | \"🏨\"\n  | \"🏩\"\n  | \"🏪\"\n  | \"🏫\"\n  | \"🏬\"\n  | \"🏭\"\n  | \"🏯\"\n  | \"🏰\"\n  | \"💒\"\n  | \"🗼\"\n  | \"🗽\"\n  | \"⛪\"\n  | \"🕌\"\n  | \"🛕\"\n  | \"🕍\"\n  | \"⛩️\"\n  | \"⛩\"\n  | \"🕋\"\n  | \"⛲\"\n  | \"⛺\"\n  | \"🌁\"\n  | \"🌃\"\n  | \"🏙️\"\n  | \"🏙\"\n  | \"🌄\"\n  | \"🌅\"\n  | \"🌆\"\n  | \"🌇\"\n  | \"🌉\"\n  | \"♨️\"\n  | \"♨\"\n  | \"🎠\"\n  | \"🛝\"\n  | \"🎡\"\n  | \"🎢\"\n  | \"💈\"\n  | \"🎪\"\n  | \"🚂\"\n  | \"🚃\"\n  | \"🚄\"\n  | \"🚅\"\n  | \"🚆\"\n  | \"🚇\"\n  | \"🚈\"\n  | \"🚉\"\n  | \"🚊\"\n  | \"🚝\"\n  | \"🚞\"\n  | \"🚋\"\n  | \"🚌\"\n  | \"🚍\"\n  | \"🚎\"\n  | \"🚐\"\n  | \"🚑\"\n  | \"🚒\"\n  | \"🚓\"\n  | \"🚔\"\n  | \"🚕\"\n  | \"🚖\"\n  | \"🚗\"\n  | \"🚘\"\n  | \"🚙\"\n  | \"🛻\"\n  | \"🚚\"\n  | \"🚛\"\n  | \"🚜\"\n  | \"🏎️\"\n  | \"🏎\"\n  | \"🏍️\"\n  | \"🏍\"\n  | \"🛵\"\n  | \"🦽\"\n  | \"🦼\"\n  | \"🛺\"\n  | \"🚲\"\n  | \"🛴\"\n  | \"🛹\"\n  | \"🛼\"\n  | \"🚏\"\n  | \"🛣️\"\n  | \"🛣\"\n  | \"🛤️\"\n  | \"🛤\"\n  | \"🛢️\"\n  | \"🛢\"\n  | \"⛽\"\n  | \"🛞\"\n  | \"🚨\"\n  | \"🚥\"\n  | \"🚦\"\n  | \"🛑\"\n  | \"🚧\"\n  | \"⚓\"\n  | \"🛟\"\n  | \"⛵\"\n  | \"🛶\"\n  | \"🚤\"\n  | \"🛳️\"\n  | \"🛳\"\n  | \"⛴️\"\n  | \"⛴\"\n  | \"🛥️\"\n  | \"🛥\"\n  | \"🚢\"\n  | \"✈️\"\n  | \"✈\"\n  | \"🛩️\"\n  | \"🛩\"\n  | \"🛫\"\n  | \"🛬\"\n  | \"🪂\"\n  | \"💺\"\n  | \"🚁\"\n  | \"🚟\"\n  | \"🚠\"\n  | \"🚡\"\n  | \"🛰️\"\n  | \"🛰\"\n  | \"🚀\"\n  | \"🛸\"\n  | \"🛎️\"\n  | \"🛎\"\n  | \"🧳\"\n  | \"⌛\"\n  | \"⏳\"\n  | \"⌚\"\n  | \"⏰\"\n  | \"⏱️\"\n  | \"⏱\"\n  | \"⏲️\"\n  | \"⏲\"\n  | \"🕰️\"\n  | \"🕰\"\n  | \"🕛\"\n  | \"🕧\"\n  | \"🕐\"\n  | \"🕜\"\n  | \"🕑\"\n  | \"🕝\"\n  | \"🕒\"\n  | \"🕞\"\n  | \"🕓\"\n  | \"🕟\"\n  | \"🕔\"\n  | \"🕠\"\n  | \"🕕\"\n  | \"🕡\"\n  | \"🕖\"\n  | \"🕢\"\n  | \"🕗\"\n  | \"🕣\"\n  | \"🕘\"\n  | \"🕤\"\n  | \"🕙\"\n  | \"🕥\"\n  | \"🕚\"\n  | \"🕦\"\n  | \"🌑\"\n  | \"🌒\"\n  | \"🌓\"\n  | \"🌔\"\n  | \"🌕\"\n  | \"🌖\"\n  | \"🌗\"\n  | \"🌘\"\n  | \"🌙\"\n  | \"🌚\"\n  | \"🌛\"\n  | \"🌜\"\n  | \"🌡️\"\n  | \"🌡\"\n  | \"☀️\"\n  | \"☀\"\n  | \"🌝\"\n  | \"🌞\"\n  | \"🪐\"\n  | \"⭐\"\n  | \"🌟\"\n  | \"🌠\"\n  | \"🌌\"\n  | \"☁️\"\n  | \"☁\"\n  | \"⛅\"\n  | \"⛈️\"\n  | \"⛈\"\n  | \"🌤️\"\n  | \"🌤\"\n  | \"🌥️\"\n  | \"🌥\"\n  | \"🌦️\"\n  | \"🌦\"\n  | \"🌧️\"\n  | \"🌧\"\n  | \"🌨️\"\n  | \"🌨\"\n  | \"🌩️\"\n  | \"🌩\"\n  | \"🌪️\"\n  | \"🌪\"\n  | \"🌫️\"\n  | \"🌫\"\n  | \"🌬️\"\n  | \"🌬\"\n  | \"🌀\"\n  | \"🌈\"\n  | \"🌂\"\n  | \"☂️\"\n  | \"☂\"\n  | \"☔\"\n  | \"⛱️\"\n  | \"⛱\"\n  | \"⚡\"\n  | \"❄️\"\n  | \"❄\"\n  | \"☃️\"\n  | \"☃\"\n  | \"⛄\"\n  | \"☄️\"\n  | \"☄\"\n  | \"🔥\"\n  | \"💧\"\n  | \"🌊\"\n  | \"🎃\"\n  | \"🎄\"\n  | \"🎆\"\n  | \"🎇\"\n  | \"🧨\"\n  | \"✨\"\n  | \"🎈\"\n  | \"🎉\"\n  | \"🎊\"\n  | \"🎋\"\n  | \"🎍\"\n  | \"🎎\"\n  | \"🎏\"\n  | \"🎐\"\n  | \"🎑\"\n  | \"🧧\"\n  | \"🎀\"\n  | \"🎁\"\n  | \"🎗️\"\n  | \"🎗\"\n  | \"🎟️\"\n  | \"🎟\"\n  | \"🎫\"\n  | \"🎖️\"\n  | \"🎖\"\n  | \"🏆\"\n  | \"🏅\"\n  | \"🥇\"\n  | \"🥈\"\n  | \"🥉\"\n  | \"⚽\"\n  | \"⚾\"\n  | \"🥎\"\n  | \"🏀\"\n  | \"🏐\"\n  | \"🏈\"\n  | \"🏉\"\n  | \"🎾\"\n  | \"🥏\"\n  | \"🎳\"\n  | \"🏏\"\n  | \"🏑\"\n  | \"🏒\"\n  | \"🥍\"\n  | \"🏓\"\n  | \"🏸\"\n  | \"🥊\"\n  | \"🥋\"\n  | \"🥅\"\n  | \"⛳\"\n  | \"⛸️\"\n  | \"⛸\"\n  | \"🎣\"\n  | \"🤿\"\n  | \"🎽\"\n  | \"🎿\"\n  | \"🛷\"\n  | \"🥌\"\n  | \"🎯\"\n  | \"🪀\"\n  | \"🪁\"\n  | \"🎱\"\n  | \"🔮\"\n  | \"🪄\"\n  | \"🧿\"\n  | \"🪬\"\n  | \"🎮\"\n  | \"🕹️\"\n  | \"🕹\"\n  | \"🎰\"\n  | \"🎲\"\n  | \"🧩\"\n  | \"🧸\"\n  | \"🪅\"\n  | \"🪩\"\n  | \"🪆\"\n  | \"♠️\"\n  | \"♠\"\n  | \"♥️\"\n  | \"♥\"\n  | \"♦️\"\n  | \"♦\"\n  | \"♣️\"\n  | \"♣\"\n  | \"♟️\"\n  | \"♟\"\n  | \"🃏\"\n  | \"🀄\"\n  | \"🎴\"\n  | \"🎭\"\n  | \"🖼️\"\n  | \"🖼\"\n  | \"🎨\"\n  | \"🧵\"\n  | \"🪡\"\n  | \"🧶\"\n  | \"🪢\"\n  | \"👓\"\n  | \"🕶️\"\n  | \"🕶\"\n  | \"🥽\"\n  | \"🥼\"\n  | \"🦺\"\n  | \"👔\"\n  | \"👕\"\n  | \"👖\"\n  | \"🧣\"\n  | \"🧤\"\n  | \"🧥\"\n  | \"🧦\"\n  | \"👗\"\n  | \"👘\"\n  | \"🥻\"\n  | \"🩱\"\n  | \"🩲\"\n  | \"🩳\"\n  | \"👙\"\n  | \"👚\"\n  | \"👛\"\n  | \"👜\"\n  | \"👝\"\n  | \"🛍️\"\n  | \"🛍\"\n  | \"🎒\"\n  | \"🩴\"\n  | \"👞\"\n  | \"👟\"\n  | \"🥾\"\n  | \"🥿\"\n  | \"👠\"\n  | \"👡\"\n  | \"🩰\"\n  | \"👢\"\n  | \"👑\"\n  | \"👒\"\n  | \"🎩\"\n  | \"🎓\"\n  | \"🧢\"\n  | \"🪖\"\n  | \"⛑️\"\n  | \"⛑\"\n  | \"📿\"\n  | \"💄\"\n  | \"💍\"\n  | \"💎\"\n  | \"🔇\"\n  | \"🔈\"\n  | \"🔉\"\n  | \"🔊\"\n  | \"📢\"\n  | \"📣\"\n  | \"📯\"\n  | \"🔔\"\n  | \"🔕\"\n  | \"🎼\"\n  | \"🎵\"\n  | \"🎶\"\n  | \"🎙️\"\n  | \"🎙\"\n  | \"🎚️\"\n  | \"🎚\"\n  | \"🎛️\"\n  | \"🎛\"\n  | \"🎤\"\n  | \"🎧\"\n  | \"📻\"\n  | \"🎷\"\n  | \"🪗\"\n  | \"🎸\"\n  | \"🎹\"\n  | \"🎺\"\n  | \"🎻\"\n  | \"🪕\"\n  | \"🥁\"\n  | \"🪘\"\n  | \"📱\"\n  | \"📲\"\n  | \"☎️\"\n  | \"☎\"\n  | \"📞\"\n  | \"📟\"\n  | \"📠\"\n  | \"🔋\"\n  | \"🪫\"\n  | \"🔌\"\n  | \"💻\"\n  | \"🖥️\"\n  | \"🖥\"\n  | \"🖨️\"\n  | \"🖨\"\n  | \"⌨️\"\n  | \"⌨\"\n  | \"🖱️\"\n  | \"🖱\"\n  | \"🖲️\"\n  | \"🖲\"\n  | \"💽\"\n  | \"💾\"\n  | \"💿\"\n  | \"📀\"\n  | \"🧮\"\n  | \"🎥\"\n  | \"🎞️\"\n  | \"🎞\"\n  | \"📽️\"\n  | \"📽\"\n  | \"🎬\"\n  | \"📺\"\n  | \"📷\"\n  | \"📸\"\n  | \"📹\"\n  | \"📼\"\n  | \"🔍\"\n  | \"🔎\"\n  | \"🕯️\"\n  | \"🕯\"\n  | \"💡\"\n  | \"🔦\"\n  | \"🏮\"\n  | \"🪔\"\n  | \"📔\"\n  | \"📕\"\n  | \"📖\"\n  | \"📗\"\n  | \"📘\"\n  | \"📙\"\n  | \"📚\"\n  | \"📓\"\n  | \"📒\"\n  | \"📃\"\n  | \"📜\"\n  | \"📄\"\n  | \"📰\"\n  | \"🗞️\"\n  | \"🗞\"\n  | \"📑\"\n  | \"🔖\"\n  | \"🏷️\"\n  | \"🏷\"\n  | \"💰\"\n  | \"🪙\"\n  | \"💴\"\n  | \"💵\"\n  | \"💶\"\n  | \"💷\"\n  | \"💸\"\n  | \"💳\"\n  | \"🧾\"\n  | \"💹\"\n  | \"✉️\"\n  | \"✉\"\n  | \"📧\"\n  | \"📨\"\n  | \"📩\"\n  | \"📤\"\n  | \"📥\"\n  | \"📦\"\n  | \"📫\"\n  | \"📪\"\n  | \"📬\"\n  | \"📭\"\n  | \"📮\"\n  | \"🗳️\"\n  | \"🗳\"\n  | \"✏️\"\n  | \"✏\"\n  | \"✒️\"\n  | \"✒\"\n  | \"🖋️\"\n  | \"🖋\"\n  | \"🖊️\"\n  | \"🖊\"\n  | \"🖌️\"\n  | \"🖌\"\n  | \"🖍️\"\n  | \"🖍\"\n  | \"📝\"\n  | \"💼\"\n  | \"📁\"\n  | \"📂\"\n  | \"🗂️\"\n  | \"🗂\"\n  | \"📅\"\n  | \"📆\"\n  | \"🗒️\"\n  | \"🗒\"\n  | \"🗓️\"\n  | \"🗓\"\n  | \"📇\"\n  | \"📈\"\n  | \"📉\"\n  | \"📊\"\n  | \"📋\"\n  | \"📌\"\n  | \"📍\"\n  | \"📎\"\n  | \"🖇️\"\n  | \"🖇\"\n  | \"📏\"\n  | \"📐\"\n  | \"✂️\"\n  | \"✂\"\n  | \"🗃️\"\n  | \"🗃\"\n  | \"🗄️\"\n  | \"🗄\"\n  | \"🗑️\"\n  | \"🗑\"\n  | \"🔒\"\n  | \"🔓\"\n  | \"🔏\"\n  | \"🔐\"\n  | \"🔑\"\n  | \"🗝️\"\n  | \"🗝\"\n  | \"🔨\"\n  | \"🪓\"\n  | \"⛏️\"\n  | \"⛏\"\n  | \"⚒️\"\n  | \"⚒\"\n  | \"🛠️\"\n  | \"🛠\"\n  | \"🗡️\"\n  | \"🗡\"\n  | \"⚔️\"\n  | \"⚔\"\n  | \"🔫\"\n  | \"🪃\"\n  | \"🏹\"\n  | \"🛡️\"\n  | \"🛡\"\n  | \"🪚\"\n  | \"🔧\"\n  | \"🪛\"\n  | \"🔩\"\n  | \"⚙️\"\n  | \"⚙\"\n  | \"🗜️\"\n  | \"🗜\"\n  | \"⚖️\"\n  | \"⚖\"\n  | \"🦯\"\n  | \"🔗\"\n  | \"⛓️\"\n  | \"⛓\"\n  | \"🪝\"\n  | \"🧰\"\n  | \"🧲\"\n  | \"🪜\"\n  | \"⚗️\"\n  | \"⚗\"\n  | \"🧪\"\n  | \"🧫\"\n  | \"🧬\"\n  | \"🔬\"\n  | \"🔭\"\n  | \"📡\"\n  | \"💉\"\n  | \"🩸\"\n  | \"💊\"\n  | \"🩹\"\n  | \"🩼\"\n  | \"🩺\"\n  | \"🩻\"\n  | \"🚪\"\n  | \"🛗\"\n  | \"🪞\"\n  | \"🪟\"\n  | \"🛏️\"\n  | \"🛏\"\n  | \"🛋️\"\n  | \"🛋\"\n  | \"🪑\"\n  | \"🚽\"\n  | \"🪠\"\n  | \"🚿\"\n  | \"🛁\"\n  | \"🪤\"\n  | \"🪒\"\n  | \"🧴\"\n  | \"🧷\"\n  | \"🧹\"\n  | \"🧺\"\n  | \"🧻\"\n  | \"🪣\"\n  | \"🧼\"\n  | \"🫧\"\n  | \"🪥\"\n  | \"🧽\"\n  | \"🧯\"\n  | \"🛒\"\n  | \"🚬\"\n  | \"⚰️\"\n  | \"⚰\"\n  | \"🪦\"\n  | \"⚱️\"\n  | \"⚱\"\n  | \"🗿\"\n  | \"🪧\"\n  | \"🪪\"\n  | \"🏧\"\n  | \"🚮\"\n  | \"🚰\"\n  | \"♿\"\n  | \"🚹\"\n  | \"🚺\"\n  | \"🚻\"\n  | \"🚼\"\n  | \"🚾\"\n  | \"🛂\"\n  | \"🛃\"\n  | \"🛄\"\n  | \"🛅\"\n  | \"⚠️\"\n  | \"⚠\"\n  | \"🚸\"\n  | \"⛔\"\n  | \"🚫\"\n  | \"🚳\"\n  | \"🚭\"\n  | \"🚯\"\n  | \"🚱\"\n  | \"🚷\"\n  | \"📵\"\n  | \"🔞\"\n  | \"☢️\"\n  | \"☢\"\n  | \"☣️\"\n  | \"☣\"\n  | \"⬆️\"\n  | \"⬆\"\n  | \"↗️\"\n  | \"↗\"\n  | \"➡️\"\n  | \"➡\"\n  | \"↘️\"\n  | \"↘\"\n  | \"⬇️\"\n  | \"⬇\"\n  | \"↙️\"\n  | \"↙\"\n  | \"⬅️\"\n  | \"⬅\"\n  | \"↖️\"\n  | \"↖\"\n  | \"↕️\"\n  | \"↕\"\n  | \"↔️\"\n  | \"↩️\"\n  | \"↩\"\n  | \"↪️\"\n  | \"↪\"\n  | \"⤴️\"\n  | \"⤴\"\n  | \"⤵️\"\n  | \"⤵\"\n  | \"🔃\"\n  | \"🔄\"\n  | \"🔙\"\n  | \"🔚\"\n  | \"🔛\"\n  | \"🔜\"\n  | \"🔝\"\n  | \"🛐\"\n  | \"⚛️\"\n  | \"⚛\"\n  | \"🕉️\"\n  | \"🕉\"\n  | \"✡️\"\n  | \"✡\"\n  | \"☸️\"\n  | \"☸\"\n  | \"☯️\"\n  | \"☯\"\n  | \"✝️\"\n  | \"✝\"\n  | \"☦️\"\n  | \"☦\"\n  | \"☪️\"\n  | \"☪\"\n  | \"☮️\"\n  | \"☮\"\n  | \"🕎\"\n  | \"🔯\"\n  | \"♈\"\n  | \"♉\"\n  | \"♊\"\n  | \"♋\"\n  | \"♌\"\n  | \"♍\"\n  | \"♎\"\n  | \"♏\"\n  | \"♐\"\n  | \"♑\"\n  | \"♒\"\n  | \"♓\"\n  | \"⛎\"\n  | \"🔀\"\n  | \"🔁\"\n  | \"🔂\"\n  | \"▶️\"\n  | \"⏩\"\n  | \"⏭️\"\n  | \"⏭\"\n  | \"⏯️\"\n  | \"⏯\"\n  | \"◀️\"\n  | \"⏪\"\n  | \"⏮️\"\n  | \"⏮\"\n  | \"🔼\"\n  | \"⏫\"\n  | \"🔽\"\n  | \"⏬\"\n  | \"⏸️\"\n  | \"⏸\"\n  | \"⏹️\"\n  | \"⏹\"\n  | \"⏺️\"\n  | \"⏺\"\n  | \"⏏️\"\n  | \"⏏\"\n  | \"🎦\"\n  | \"🔅\"\n  | \"🔆\"\n  | \"📶\"\n  | \"📳\"\n  | \"📴\"\n  | \"♀️\"\n  | \"♀\"\n  | \"♂️\"\n  | \"♂\"\n  | \"⚧️\"\n  | \"⚧\"\n  | \"✖️\"\n  | \"✖\"\n  | \"➕\"\n  | \"➖\"\n  | \"➗\"\n  | \"🟰\"\n  | \"♾️\"\n  | \"♾\"\n  | \"‼️\"\n  | \"‼\"\n  | \"⁉️\"\n  | \"⁉\"\n  | \"❓\"\n  | \"❔\"\n  | \"❕\"\n  | \"❗\"\n  | \"〰️\"\n  | \"〰\"\n  | \"💱\"\n  | \"💲\"\n  | \"⚕️\"\n  | \"⚕\"\n  | \"♻️\"\n  | \"♻\"\n  | \"⚜️\"\n  | \"⚜\"\n  | \"🔱\"\n  | \"📛\"\n  | \"🔰\"\n  | \"⭕\"\n  | \"✅\"\n  | \"☑️\"\n  | \"☑\"\n  | \"✔️\"\n  | \"✔\"\n  | \"❌\"\n  | \"❎\"\n  | \"➰\"\n  | \"➿\"\n  | \"〽️\"\n  | \"〽\"\n  | \"✳️\"\n  | \"✳\"\n  | \"✴️\"\n  | \"✴\"\n  | \"❇️\"\n  | \"❇\"\n  | \"©️\"\n  | \"©\"\n  | \"®️\"\n  | \"®\"\n  | \"™️\"\n  | \"#️⃣\"\n  | \"#⃣\"\n  | \"*️⃣\"\n  | \"*⃣\"\n  | \"0️⃣\"\n  | \"0⃣\"\n  | \"1️⃣\"\n  | \"1⃣\"\n  | \"2️⃣\"\n  | \"2⃣\"\n  | \"3️⃣\"\n  | \"3⃣\"\n  | \"4️⃣\"\n  | \"4⃣\"\n  | \"5️⃣\"\n  | \"5⃣\"\n  | \"6️⃣\"\n  | \"6⃣\"\n  | \"7️⃣\"\n  | \"7⃣\"\n  | \"8️⃣\"\n  | \"8⃣\"\n  | \"9️⃣\"\n  | \"9⃣\"\n  | \"🔟\"\n  | \"🔠\"\n  | \"🔡\"\n  | \"🔢\"\n  | \"🔣\"\n  | \"🔤\"\n  | \"🅰️\"\n  | \"🅰\"\n  | \"🆎\"\n  | \"🅱️\"\n  | \"🅱\"\n  | \"🆑\"\n  | \"🆒\"\n  | \"🆓\"\n  | \"ℹ️\"\n  | \"ℹ\"\n  | \"🆔\"\n  | \"Ⓜ️\"\n  | \"Ⓜ\"\n  | \"🆕\"\n  | \"🆖\"\n  | \"🅾️\"\n  | \"🅾\"\n  | \"🆗\"\n  | \"🅿️\"\n  | \"🅿\"\n  | \"🆘\"\n  | \"🆙\"\n  | \"🆚\"\n  | \"🈁\"\n  | \"🈂️\"\n  | \"🈂\"\n  | \"🈷️\"\n  | \"🈷\"\n  | \"🈶\"\n  | \"🈯\"\n  | \"🉐\"\n  | \"🈹\"\n  | \"🈚\"\n  | \"🈲\"\n  | \"🉑\"\n  | \"🈸\"\n  | \"🈴\"\n  | \"🈳\"\n  | \"㊗️\"\n  | \"㊗\"\n  | \"㊙️\"\n  | \"㊙\"\n  | \"🈺\"\n  | \"🈵\"\n  | \"🔴\"\n  | \"🟠\"\n  | \"🟡\"\n  | \"🟢\"\n  | \"🔵\"\n  | \"🟣\"\n  | \"🟤\"\n  | \"⚫\"\n  | \"⚪\"\n  | \"🟥\"\n  | \"🟧\"\n  | \"🟨\"\n  | \"🟩\"\n  | \"🟦\"\n  | \"🟪\"\n  | \"🟫\"\n  | \"⬛\"\n  | \"⬜\"\n  | \"◼️\"\n  | \"◼\"\n  | \"◻️\"\n  | \"◻\"\n  | \"◾\"\n  | \"◽\"\n  | \"▪️\"\n  | \"▪\"\n  | \"▫️\"\n  | \"▫\"\n  | \"🔶\"\n  | \"🔷\"\n  | \"🔸\"\n  | \"🔹\"\n  | \"🔺\"\n  | \"🔻\"\n  | \"💠\"\n  | \"🔘\"\n  | \"🔳\"\n  | \"🔲\"\n  | \"🏁\"\n  | \"🚩\"\n  | \"🎌\"\n  | \"🏴\"\n  | \"🏳️\"\n  | \"🏳\"\n  | \"🏳️‍🌈\"\n  | \"🏳‍🌈\"\n  | \"🏳️‍⚧️\"\n  | \"🏴‍☠️\"\n  | \"🏴‍☠\"\n  | \"🇦🇨\"\n  | \"🇦🇩\"\n  | \"🇦🇪\"\n  | \"🇦🇫\"\n  | \"🇦🇬\"\n  | \"🇦🇮\"\n  | \"🇦🇱\"\n  | \"🇦🇲\"\n  | \"🇦🇴\"\n  | \"🇦🇶\"\n  | \"🇦🇷\"\n  | \"🇦🇸\"\n  | \"🇦🇹\"\n  | \"🇦🇺\"\n  | \"🇦🇼\"\n  | \"🇦🇽\"\n  | \"🇦🇿\"\n  | \"🇧🇦\"\n  | \"🇧🇧\"\n  | \"🇧🇩\"\n  | \"🇧🇪\"\n  | \"🇧🇫\"\n  | \"🇧🇬\"\n  | \"🇧🇭\"\n  | \"🇧🇮\"\n  | \"🇧🇯\"\n  | \"🇧🇱\"\n  | \"🇧🇲\"\n  | \"🇧🇳\"\n  | \"🇧🇴\"\n  | \"🇧🇶\"\n  | \"🇧🇷\"\n  | \"🇧🇸\"\n  | \"🇧🇹\"\n  | \"🇧🇻\"\n  | \"🇧🇼\"\n  | \"🇧🇾\"\n  | \"🇧🇿\"\n  | \"🇨🇦\"\n  | \"🇨🇨\"\n  | \"🇨🇩\"\n  | \"🇨🇫\"\n  | \"🇨🇬\"\n  | \"🇨🇭\"\n  | \"🇨🇮\"\n  | \"🇨🇰\"\n  | \"🇨🇱\"\n  | \"🇨🇲\"\n  | \"🇨🇳\"\n  | \"🇨🇴\"\n  | \"🇨🇵\"\n  | \"🇨🇷\"\n  | \"🇨🇺\"\n  | \"🇨🇻\"\n  | \"🇨🇼\"\n  | \"🇨🇽\"\n  | \"🇨🇾\"\n  | \"🇨🇿\"\n  | \"🇩🇪\"\n  | \"🇩🇬\"\n  | \"🇩🇯\"\n  | \"🇩🇰\"\n  | \"🇩🇲\"\n  | \"🇩🇴\"\n  | \"🇩🇿\"\n  | \"🇪🇦\"\n  | \"🇪🇨\"\n  | \"🇪🇪\"\n  | \"🇪🇬\"\n  | \"🇪🇭\"\n  | \"🇪🇷\"\n  | \"🇪🇸\"\n  | \"🇪🇹\"\n  | \"🇪🇺\"\n  | \"🇫🇮\"\n  | \"🇫🇯\"\n  | \"🇫🇰\"\n  | \"🇫🇲\"\n  | \"🇫🇴\"\n  | \"🇫🇷\"\n  | \"🇬🇦\"\n  | \"🇬🇧\"\n  | \"🇬🇩\"\n  | \"🇬🇪\"\n  | \"🇬🇫\"\n  | \"🇬🇬\"\n  | \"🇬🇭\"\n  | \"🇬🇮\"\n  | \"🇬🇱\"\n  | \"🇬🇲\"\n  | \"🇬🇳\"\n  | \"🇬🇵\"\n  | \"🇬🇶\"\n  | \"🇬🇷\"\n  | \"🇬🇸\"\n  | \"🇬🇹\"\n  | \"🇬🇺\"\n  | \"🇬🇼\"\n  | \"🇬🇾\"\n  | \"🇭🇰\"\n  | \"🇭🇲\"\n  | \"🇭🇳\"\n  | \"🇭🇷\"\n  | \"🇭🇹\"\n  | \"🇭🇺\"\n  | \"🇮🇨\"\n  | \"🇮🇩\"\n  | \"🇮🇪\"\n  | \"🇮🇱\"\n  | \"🇮🇲\"\n  | \"🇮🇳\"\n  | \"🇮🇴\"\n  | \"🇮🇶\"\n  | \"🇮🇷\"\n  | \"🇮🇸\"\n  | \"🇮🇹\"\n  | \"🇯🇪\"\n  | \"🇯🇲\"\n  | \"🇯🇴\"\n  | \"🇯🇵\"\n  | \"🇰🇪\"\n  | \"🇰🇬\"\n  | \"🇰🇭\"\n  | \"🇰🇮\"\n  | \"🇰🇲\"\n  | \"🇰🇳\"\n  | \"🇰🇵\"\n  | \"🇰🇷\"\n  | \"🇰🇼\"\n  | \"🇰🇾\"\n  | \"🇰🇿\"\n  | \"🇱🇦\"\n  | \"🇱🇧\"\n  | \"🇱🇨\"\n  | \"🇱🇮\"\n  | \"🇱🇰\"\n  | \"🇱🇷\"\n  | \"🇱🇸\"\n  | \"🇱🇹\"\n  | \"🇱🇺\"\n  | \"🇱🇻\"\n  | \"🇱🇾\"\n  | \"🇲🇦\"\n  | \"🇲🇨\"\n  | \"🇲🇩\"\n  | \"🇲🇪\"\n  | \"🇲🇫\"\n  | \"🇲🇬\"\n  | \"🇲🇭\"\n  | \"🇲🇰\"\n  | \"🇲🇱\"\n  | \"🇲🇲\"\n  | \"🇲🇳\"\n  | \"🇲🇴\"\n  | \"🇲🇵\"\n  | \"🇲🇶\"\n  | \"🇲🇷\"\n  | \"🇲🇸\"\n  | \"🇲🇹\"\n  | \"🇲🇺\"\n  | \"🇲🇻\"\n  | \"🇲🇼\"\n  | \"🇲🇽\"\n  | \"🇲🇾\"\n  | \"🇲🇿\"\n  | \"🇳🇦\"\n  | \"🇳🇨\"\n  | \"🇳🇪\"\n  | \"🇳🇫\"\n  | \"🇳🇬\"\n  | \"🇳🇮\"\n  | \"🇳🇱\"\n  | \"🇳🇴\"\n  | \"🇳🇵\"\n  | \"🇳🇷\"\n  | \"🇳🇺\"\n  | \"🇳🇿\"\n  | \"🇴🇲\"\n  | \"🇵🇦\"\n  | \"🇵🇪\"\n  | \"🇵🇫\"\n  | \"🇵🇬\"\n  | \"🇵🇭\"\n  | \"🇵🇰\"\n  | \"🇵🇱\"\n  | \"🇵🇲\"\n  | \"🇵🇳\"\n  | \"🇵🇷\"\n  | \"🇵🇸\"\n  | \"🇵🇹\"\n  | \"🇵🇼\"\n  | \"🇵🇾\"\n  | \"🇶🇦\"\n  | \"🇷🇪\"\n  | \"🇷🇴\"\n  | \"🇷🇸\"\n  | \"🇷🇺\"\n  | \"🇷🇼\"\n  | \"🇸🇦\"\n  | \"🇸🇧\"\n  | \"🇸🇨\"\n  | \"🇸🇩\"\n  | \"🇸🇪\"\n  | \"🇸🇬\"\n  | \"🇸🇭\"\n  | \"🇸🇮\"\n  | \"🇸🇯\"\n  | \"🇸🇰\"\n  | \"🇸🇱\"\n  | \"🇸🇲\"\n  | \"🇸🇳\"\n  | \"🇸🇴\"\n  | \"🇸🇷\"\n  | \"🇸🇸\"\n  | \"🇸🇹\"\n  | \"🇸🇻\"\n  | \"🇸🇽\"\n  | \"🇸🇾\"\n  | \"🇸🇿\"\n  | \"🇹🇦\"\n  | \"🇹🇨\"\n  | \"🇹🇩\"\n  | \"🇹🇫\"\n  | \"🇹🇬\"\n  | \"🇹🇭\"\n  | \"🇹🇯\"\n  | \"🇹🇰\"\n  | \"🇹🇱\"\n  | \"🇹🇲\"\n  | \"🇹🇳\"\n  | \"🇹🇴\"\n  | \"🇹🇷\"\n  | \"🇹🇹\"\n  | \"🇹🇻\"\n  | \"🇹🇼\"\n  | \"🇹🇿\"\n  | \"🇺🇦\"\n  | \"🇺🇬\"\n  | \"🇺🇲\"\n  | \"🇺🇳\"\n  | \"🇺🇸\"\n  | \"🇺🇾\"\n  | \"🇺🇿\"\n  | \"🇻🇦\"\n  | \"🇻🇨\"\n  | \"🇻🇪\"\n  | \"🇻🇬\"\n  | \"🇻🇮\"\n  | \"🇻🇳\"\n  | \"🇻🇺\"\n  | \"🇼🇫\"\n  | \"🇼🇸\"\n  | \"🇽🇰\"\n  | \"🇾🇪\"\n  | \"🇾🇹\"\n  | \"🇿🇦\"\n  | \"🇿🇲\"\n  | \"🇿🇼\"\n  | \"🏴󠁧󠁢󠁥󠁮󠁧󠁿\"\n  | \"🏴󠁧󠁢󠁳󠁣󠁴󠁿\"\n  | \"🏴󠁧󠁢󠁷󠁬󠁳󠁿\"\n\nexport type PageObjectResponse = {\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  properties: Record<\n    string,\n    | { type: \"number\"; number: number | null; id: string }\n    | { type: \"url\"; url: string | null; id: string }\n    | { type: \"select\"; select: PartialSelectResponse | null; id: string }\n    | {\n        type: \"multi_select\"\n        multi_select: Array<PartialSelectResponse>\n        id: string\n      }\n    | { type: \"status\"; status: PartialSelectResponse | null; id: string }\n    | { type: \"date\"; date: DateResponse | null; id: string }\n    | { type: \"email\"; email: string | null; id: string }\n    | { type: \"phone_number\"; phone_number: string | null; id: string }\n    | { type: \"checkbox\"; checkbox: boolean; id: string }\n    | {\n        type: \"files\"\n        files: Array<\n          | {\n              file: { url: string; expiry_time: string }\n              name: StringRequest\n              type?: \"file\"\n            }\n          | {\n              external: { url: TextRequest }\n              name: StringRequest\n              type?: \"external\"\n            }\n        >\n        id: string\n      }\n    | {\n        type: \"created_by\"\n        created_by: PartialUserObjectResponse | UserObjectResponse\n        id: string\n      }\n    | { type: \"created_time\"; created_time: string; id: string }\n    | {\n        type: \"last_edited_by\"\n        last_edited_by: PartialUserObjectResponse | UserObjectResponse\n        id: string\n      }\n    | { type: \"last_edited_time\"; last_edited_time: string; id: string }\n    | { type: \"formula\"; formula: FormulaPropertyResponse; id: string }\n    | { type: \"button\"; button: Record<string, never>; id: string }\n    | {\n        type: \"unique_id\"\n        unique_id: { prefix: string | null; number: number | null }\n        id: string\n      }\n    | {\n        type: \"verification\"\n        verification:\n          | VerificationPropertyUnverifiedResponse\n          | null\n          | VerificationPropertyResponse\n          | null\n        id: string\n      }\n    | { type: \"title\"; title: Array<RichTextItemResponse>; id: string }\n    | { type: \"rich_text\"; rich_text: Array<RichTextItemResponse>; id: string }\n    | {\n        type: \"people\"\n        people: Array<PartialUserObjectResponse | UserObjectResponse>\n        id: string\n      }\n    | { type: \"relation\"; relation: Array<{ id: string }>; id: string }\n    | {\n        type: \"rollup\"\n        rollup:\n          | { type: \"number\"; number: number | null; function: RollupFunction }\n          | {\n              type: \"date\"\n              date: DateResponse | null\n              function: RollupFunction\n            }\n          | {\n              type: \"array\"\n              array: Array<\n                | { type: \"number\"; number: number | null }\n                | { type: \"url\"; url: string | null }\n                | { type: \"select\"; select: PartialSelectResponse | null }\n                | {\n                    type: \"multi_select\"\n                    multi_select: Array<PartialSelectResponse>\n                  }\n                | { type: \"status\"; status: PartialSelectResponse | null }\n                | { type: \"date\"; date: DateResponse | null }\n                | { type: \"email\"; email: string | null }\n                | { type: \"phone_number\"; phone_number: string | null }\n                | { type: \"checkbox\"; checkbox: boolean }\n                | {\n                    type: \"files\"\n                    files: Array<\n                      | {\n                          file: { url: string; expiry_time: string }\n                          name: StringRequest\n                          type?: \"file\"\n                        }\n                      | {\n                          external: { url: TextRequest }\n                          name: StringRequest\n                          type?: \"external\"\n                        }\n                    >\n                  }\n                | {\n                    type: \"created_by\"\n                    created_by: PartialUserObjectResponse | UserObjectResponse\n                  }\n                | { type: \"created_time\"; created_time: string }\n                | {\n                    type: \"last_edited_by\"\n                    last_edited_by:\n                      | PartialUserObjectResponse\n                      | UserObjectResponse\n                  }\n                | { type: \"last_edited_time\"; last_edited_time: string }\n                | { type: \"formula\"; formula: FormulaPropertyResponse }\n                | { type: \"button\"; button: Record<string, never> }\n                | {\n                    type: \"unique_id\"\n                    unique_id: { prefix: string | null; number: number | null }\n                  }\n                | {\n                    type: \"verification\"\n                    verification:\n                      | VerificationPropertyUnverifiedResponse\n                      | null\n                      | VerificationPropertyResponse\n                      | null\n                  }\n                | { type: \"title\"; title: Array<RichTextItemResponse> }\n                | { type: \"rich_text\"; rich_text: Array<RichTextItemResponse> }\n                | {\n                    type: \"people\"\n                    people: Array<\n                      PartialUserObjectResponse | UserObjectResponse\n                    >\n                  }\n                | { type: \"relation\"; relation: Array<{ id: string }> }\n              >\n              function: RollupFunction\n            }\n        id: string\n      }\n  >\n  icon:\n    | { type: \"emoji\"; emoji: EmojiRequest }\n    | null\n    | { type: \"external\"; external: { url: TextRequest } }\n    | null\n    | { type: \"file\"; file: { url: string; expiry_time: string } }\n    | null\n    | { type: \"custom_emoji\"; custom_emoji: CustomEmojiResponse }\n    | null\n  cover:\n    | { type: \"external\"; external: { url: TextRequest } }\n    | null\n    | { type: \"file\"; file: { url: string; expiry_time: string } }\n    | null\n  created_by: PartialUserObjectResponse\n  last_edited_by: PartialUserObjectResponse\n  object: \"page\"\n  id: string\n  created_time: string\n  last_edited_time: string\n  archived: boolean\n  in_trash: boolean\n  url: string\n  public_url: string | null\n}\n\nexport type PartialPageObjectResponse = { object: \"page\"; id: string }\n\ntype NumberFormat =\n  | \"number\"\n  | \"number_with_commas\"\n  | \"percent\"\n  | \"dollar\"\n  | \"australian_dollar\"\n  | \"canadian_dollar\"\n  | \"singapore_dollar\"\n  | \"euro\"\n  | \"pound\"\n  | \"yen\"\n  | \"ruble\"\n  | \"rupee\"\n  | \"won\"\n  | \"yuan\"\n  | \"real\"\n  | \"lira\"\n  | \"rupiah\"\n  | \"franc\"\n  | \"hong_kong_dollar\"\n  | \"new_zealand_dollar\"\n  | \"krona\"\n  | \"norwegian_krone\"\n  | \"mexican_peso\"\n  | \"rand\"\n  | \"new_taiwan_dollar\"\n  | \"danish_krone\"\n  | \"zloty\"\n  | \"baht\"\n  | \"forint\"\n  | \"koruna\"\n  | \"shekel\"\n  | \"chilean_peso\"\n  | \"philippine_peso\"\n  | \"dirham\"\n  | \"colombian_peso\"\n  | \"riyal\"\n  | \"ringgit\"\n  | \"leu\"\n  | \"argentine_peso\"\n  | \"uruguayan_peso\"\n  | \"peruvian_sol\"\n\ntype PropertyDescriptionRequest = string\n\ntype NumberDatabasePropertyConfigResponse = {\n  type: \"number\"\n  number: { format: NumberFormat }\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype FormulaDatabasePropertyConfigResponse = {\n  type: \"formula\"\n  formula: { expression: string }\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype SelectPropertyResponse = {\n  id: StringRequest\n  name: StringRequest\n  color: SelectColor\n  description: StringRequest | null\n}\n\ntype SelectDatabasePropertyConfigResponse = {\n  type: \"select\"\n  select: { options: Array<SelectPropertyResponse> }\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype MultiSelectDatabasePropertyConfigResponse = {\n  type: \"multi_select\"\n  multi_select: { options: Array<SelectPropertyResponse> }\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype StatusPropertyResponse = {\n  id: StringRequest\n  name: StringRequest\n  color: SelectColor\n  description: StringRequest | null\n}\n\ntype StatusDatabasePropertyConfigResponse = {\n  type: \"status\"\n  status: {\n    options: Array<StatusPropertyResponse>\n    groups: Array<{\n      id: StringRequest\n      name: StringRequest\n      color: SelectColor\n      option_ids: Array<string>\n    }>\n  }\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype SinglePropertyDatabasePropertyRelationConfigResponse = {\n  type: \"single_property\"\n  single_property: EmptyObject\n  database_id: IdRequest\n}\n\ntype DualPropertyDatabasePropertyRelationConfigResponse = {\n  type: \"dual_property\"\n  dual_property: {\n    synced_property_id: StringRequest\n    synced_property_name: StringRequest\n  }\n  database_id: IdRequest\n}\n\ntype DatabasePropertyRelationConfigResponse =\n  | SinglePropertyDatabasePropertyRelationConfigResponse\n  | DualPropertyDatabasePropertyRelationConfigResponse\n\ntype RelationDatabasePropertyConfigResponse = {\n  type: \"relation\"\n  relation: DatabasePropertyRelationConfigResponse\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype RollupDatabasePropertyConfigResponse = {\n  type: \"rollup\"\n  rollup: {\n    rollup_property_name: string\n    relation_property_name: string\n    rollup_property_id: string\n    relation_property_id: string\n    function: RollupFunction\n  }\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype UniqueIdDatabasePropertyConfigResponse = {\n  type: \"unique_id\"\n  unique_id: { prefix: string | null }\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype TitleDatabasePropertyConfigResponse = {\n  type: \"title\"\n  title: EmptyObject\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype RichTextDatabasePropertyConfigResponse = {\n  type: \"rich_text\"\n  rich_text: EmptyObject\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype UrlDatabasePropertyConfigResponse = {\n  type: \"url\"\n  url: EmptyObject\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype PeopleDatabasePropertyConfigResponse = {\n  type: \"people\"\n  people: EmptyObject\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype FilesDatabasePropertyConfigResponse = {\n  type: \"files\"\n  files: EmptyObject\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype EmailDatabasePropertyConfigResponse = {\n  type: \"email\"\n  email: EmptyObject\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype PhoneNumberDatabasePropertyConfigResponse = {\n  type: \"phone_number\"\n  phone_number: EmptyObject\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype DateDatabasePropertyConfigResponse = {\n  type: \"date\"\n  date: EmptyObject\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype CheckboxDatabasePropertyConfigResponse = {\n  type: \"checkbox\"\n  checkbox: EmptyObject\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype CreatedByDatabasePropertyConfigResponse = {\n  type: \"created_by\"\n  created_by: EmptyObject\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype CreatedTimeDatabasePropertyConfigResponse = {\n  type: \"created_time\"\n  created_time: EmptyObject\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype LastEditedByDatabasePropertyConfigResponse = {\n  type: \"last_edited_by\"\n  last_edited_by: EmptyObject\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype LastEditedTimeDatabasePropertyConfigResponse = {\n  type: \"last_edited_time\"\n  last_edited_time: EmptyObject\n  id: string\n  name: string\n  description: PropertyDescriptionRequest | null\n}\n\ntype DatabasePropertyConfigResponse =\n  | NumberDatabasePropertyConfigResponse\n  | FormulaDatabasePropertyConfigResponse\n  | SelectDatabasePropertyConfigResponse\n  | MultiSelectDatabasePropertyConfigResponse\n  | StatusDatabasePropertyConfigResponse\n  | RelationDatabasePropertyConfigResponse\n  | RollupDatabasePropertyConfigResponse\n  | UniqueIdDatabasePropertyConfigResponse\n  | TitleDatabasePropertyConfigResponse\n  | RichTextDatabasePropertyConfigResponse\n  | UrlDatabasePropertyConfigResponse\n  | PeopleDatabasePropertyConfigResponse\n  | FilesDatabasePropertyConfigResponse\n  | EmailDatabasePropertyConfigResponse\n  | PhoneNumberDatabasePropertyConfigResponse\n  | DateDatabasePropertyConfigResponse\n  | CheckboxDatabasePropertyConfigResponse\n  | CreatedByDatabasePropertyConfigResponse\n  | CreatedTimeDatabasePropertyConfigResponse\n  | LastEditedByDatabasePropertyConfigResponse\n  | LastEditedTimeDatabasePropertyConfigResponse\n\nexport type PartialDatabaseObjectResponse = {\n  object: \"database\"\n  id: string\n  properties: Record<string, DatabasePropertyConfigResponse>\n}\n\nexport type DatabaseObjectResponse = {\n  title: Array<RichTextItemResponse>\n  description: Array<RichTextItemResponse>\n  icon:\n    | { type: \"emoji\"; emoji: EmojiRequest }\n    | null\n    | { type: \"external\"; external: { url: TextRequest } }\n    | null\n    | { type: \"file\"; file: { url: string; expiry_time: string } }\n    | null\n    | { type: \"custom_emoji\"; custom_emoji: CustomEmojiResponse }\n    | null\n  cover:\n    | { type: \"external\"; external: { url: TextRequest } }\n    | null\n    | { type: \"file\"; file: { url: string; expiry_time: string } }\n    | null\n  properties: Record<string, DatabasePropertyConfigResponse>\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  created_by: PartialUserObjectResponse\n  last_edited_by: PartialUserObjectResponse\n  is_inline: boolean\n  object: \"database\"\n  id: string\n  created_time: string\n  last_edited_time: string\n  archived: boolean\n  in_trash: boolean\n  url: string\n  public_url: string | null\n}\n\nexport type PartialBlockObjectResponse = { object: \"block\"; id: string }\n\ntype ApiColor =\n  | \"default\"\n  | \"gray\"\n  | \"brown\"\n  | \"orange\"\n  | \"yellow\"\n  | \"green\"\n  | \"blue\"\n  | \"purple\"\n  | \"pink\"\n  | \"red\"\n  | \"default_background\"\n  | \"gray_background\"\n  | \"brown_background\"\n  | \"orange_background\"\n  | \"yellow_background\"\n  | \"green_background\"\n  | \"blue_background\"\n  | \"purple_background\"\n  | \"pink_background\"\n  | \"red_background\"\n\nexport type ParagraphBlockObjectResponse = {\n  type: \"paragraph\"\n  paragraph: { rich_text: Array<RichTextItemResponse>; color: ApiColor }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type Heading1BlockObjectResponse = {\n  type: \"heading_1\"\n  heading_1: {\n    rich_text: Array<RichTextItemResponse>\n    color: ApiColor\n    is_toggleable: boolean\n  }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type Heading2BlockObjectResponse = {\n  type: \"heading_2\"\n  heading_2: {\n    rich_text: Array<RichTextItemResponse>\n    color: ApiColor\n    is_toggleable: boolean\n  }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type Heading3BlockObjectResponse = {\n  type: \"heading_3\"\n  heading_3: {\n    rich_text: Array<RichTextItemResponse>\n    color: ApiColor\n    is_toggleable: boolean\n  }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type BulletedListItemBlockObjectResponse = {\n  type: \"bulleted_list_item\"\n  bulleted_list_item: {\n    rich_text: Array<RichTextItemResponse>\n    color: ApiColor\n  }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type NumberedListItemBlockObjectResponse = {\n  type: \"numbered_list_item\"\n  numbered_list_item: {\n    rich_text: Array<RichTextItemResponse>\n    color: ApiColor\n  }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type QuoteBlockObjectResponse = {\n  type: \"quote\"\n  quote: { rich_text: Array<RichTextItemResponse>; color: ApiColor }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type ToDoBlockObjectResponse = {\n  type: \"to_do\"\n  to_do: {\n    rich_text: Array<RichTextItemResponse>\n    color: ApiColor\n    checked: boolean\n  }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type ToggleBlockObjectResponse = {\n  type: \"toggle\"\n  toggle: { rich_text: Array<RichTextItemResponse>; color: ApiColor }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type TemplateBlockObjectResponse = {\n  type: \"template\"\n  template: { rich_text: Array<RichTextItemResponse> }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type SyncedBlockBlockObjectResponse = {\n  type: \"synced_block\"\n  synced_block: {\n    synced_from: { type: \"block_id\"; block_id: IdRequest } | null\n  }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type ChildPageBlockObjectResponse = {\n  type: \"child_page\"\n  child_page: { title: string }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type ChildDatabaseBlockObjectResponse = {\n  type: \"child_database\"\n  child_database: { title: string }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type EquationBlockObjectResponse = {\n  type: \"equation\"\n  equation: { expression: string }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\ntype LanguageRequest =\n  | \"abap\"\n  | \"agda\"\n  | \"arduino\"\n  | \"ascii art\"\n  | \"assembly\"\n  | \"bash\"\n  | \"basic\"\n  | \"bnf\"\n  | \"c\"\n  | \"c#\"\n  | \"c++\"\n  | \"clojure\"\n  | \"coffeescript\"\n  | \"coq\"\n  | \"css\"\n  | \"dart\"\n  | \"dhall\"\n  | \"diff\"\n  | \"docker\"\n  | \"ebnf\"\n  | \"elixir\"\n  | \"elm\"\n  | \"erlang\"\n  | \"f#\"\n  | \"flow\"\n  | \"fortran\"\n  | \"gherkin\"\n  | \"glsl\"\n  | \"go\"\n  | \"graphql\"\n  | \"groovy\"\n  | \"haskell\"\n  | \"hcl\"\n  | \"html\"\n  | \"idris\"\n  | \"java\"\n  | \"javascript\"\n  | \"json\"\n  | \"julia\"\n  | \"kotlin\"\n  | \"latex\"\n  | \"less\"\n  | \"lisp\"\n  | \"livescript\"\n  | \"llvm ir\"\n  | \"lua\"\n  | \"makefile\"\n  | \"markdown\"\n  | \"markup\"\n  | \"matlab\"\n  | \"mathematica\"\n  | \"mermaid\"\n  | \"nix\"\n  | \"notion formula\"\n  | \"objective-c\"\n  | \"ocaml\"\n  | \"pascal\"\n  | \"perl\"\n  | \"php\"\n  | \"plain text\"\n  | \"powershell\"\n  | \"prolog\"\n  | \"protobuf\"\n  | \"purescript\"\n  | \"python\"\n  | \"r\"\n  | \"racket\"\n  | \"reason\"\n  | \"ruby\"\n  | \"rust\"\n  | \"sass\"\n  | \"scala\"\n  | \"scheme\"\n  | \"scss\"\n  | \"shell\"\n  | \"smalltalk\"\n  | \"solidity\"\n  | \"sql\"\n  | \"swift\"\n  | \"toml\"\n  | \"typescript\"\n  | \"vb.net\"\n  | \"verilog\"\n  | \"vhdl\"\n  | \"visual basic\"\n  | \"webassembly\"\n  | \"xml\"\n  | \"yaml\"\n  | \"java/c/c++/c#\"\n\nexport type CodeBlockObjectResponse = {\n  type: \"code\"\n  code: {\n    rich_text: Array<RichTextItemResponse>\n    caption: Array<RichTextItemResponse>\n    language: LanguageRequest\n  }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type CalloutBlockObjectResponse = {\n  type: \"callout\"\n  callout: {\n    rich_text: Array<RichTextItemResponse>\n    color: ApiColor\n    icon:\n      | { type: \"emoji\"; emoji: EmojiRequest }\n      | null\n      | { type: \"external\"; external: { url: TextRequest } }\n      | null\n      | { type: \"file\"; file: { url: string; expiry_time: string } }\n      | null\n      | { type: \"custom_emoji\"; custom_emoji: CustomEmojiResponse }\n      | null\n  }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type DividerBlockObjectResponse = {\n  type: \"divider\"\n  divider: EmptyObject\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type BreadcrumbBlockObjectResponse = {\n  type: \"breadcrumb\"\n  breadcrumb: EmptyObject\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type TableOfContentsBlockObjectResponse = {\n  type: \"table_of_contents\"\n  table_of_contents: { color: ApiColor }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type ColumnListBlockObjectResponse = {\n  type: \"column_list\"\n  column_list: EmptyObject\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type ColumnBlockObjectResponse = {\n  type: \"column\"\n  column: EmptyObject\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type LinkToPageBlockObjectResponse = {\n  type: \"link_to_page\"\n  link_to_page:\n    | { type: \"page_id\"; page_id: IdRequest }\n    | { type: \"database_id\"; database_id: IdRequest }\n    | { type: \"comment_id\"; comment_id: IdRequest }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type TableBlockObjectResponse = {\n  type: \"table\"\n  table: {\n    has_column_header: boolean\n    has_row_header: boolean\n    table_width: number\n  }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type TableRowBlockObjectResponse = {\n  type: \"table_row\"\n  table_row: { cells: Array<Array<RichTextItemResponse>> }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type EmbedBlockObjectResponse = {\n  type: \"embed\"\n  embed: { url: string; caption: Array<RichTextItemResponse> }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type BookmarkBlockObjectResponse = {\n  type: \"bookmark\"\n  bookmark: { url: string; caption: Array<RichTextItemResponse> }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type ImageBlockObjectResponse = {\n  type: \"image\"\n  image:\n    | {\n        type: \"external\"\n        external: { url: TextRequest }\n        caption: Array<RichTextItemResponse>\n      }\n    | {\n        type: \"file\"\n        file: { url: string; expiry_time: string }\n        caption: Array<RichTextItemResponse>\n      }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type VideoBlockObjectResponse = {\n  type: \"video\"\n  video:\n    | {\n        type: \"external\"\n        external: { url: TextRequest }\n        caption: Array<RichTextItemResponse>\n      }\n    | {\n        type: \"file\"\n        file: { url: string; expiry_time: string }\n        caption: Array<RichTextItemResponse>\n      }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type PdfBlockObjectResponse = {\n  type: \"pdf\"\n  pdf:\n    | {\n        type: \"external\"\n        external: { url: TextRequest }\n        caption: Array<RichTextItemResponse>\n      }\n    | {\n        type: \"file\"\n        file: { url: string; expiry_time: string }\n        caption: Array<RichTextItemResponse>\n      }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type FileBlockObjectResponse = {\n  type: \"file\"\n  file:\n    | {\n        type: \"external\"\n        external: { url: TextRequest }\n        caption: Array<RichTextItemResponse>\n        name: string\n      }\n    | {\n        type: \"file\"\n        file: { url: string; expiry_time: string }\n        caption: Array<RichTextItemResponse>\n        name: string\n      }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type AudioBlockObjectResponse = {\n  type: \"audio\"\n  audio:\n    | {\n        type: \"external\"\n        external: { url: TextRequest }\n        caption: Array<RichTextItemResponse>\n      }\n    | {\n        type: \"file\"\n        file: { url: string; expiry_time: string }\n        caption: Array<RichTextItemResponse>\n      }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type LinkPreviewBlockObjectResponse = {\n  type: \"link_preview\"\n  link_preview: { url: TextRequest }\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type UnsupportedBlockObjectResponse = {\n  type: \"unsupported\"\n  unsupported: EmptyObject\n  parent:\n    | { type: \"database_id\"; database_id: string }\n    | { type: \"page_id\"; page_id: string }\n    | { type: \"block_id\"; block_id: string }\n    | { type: \"workspace\"; workspace: true }\n  object: \"block\"\n  id: string\n  created_time: string\n  created_by: PartialUserObjectResponse\n  last_edited_time: string\n  last_edited_by: PartialUserObjectResponse\n  has_children: boolean\n  archived: boolean\n  in_trash: boolean\n}\n\nexport type BlockObjectResponse =\n  | ParagraphBlockObjectResponse\n  | Heading1BlockObjectResponse\n  | Heading2BlockObjectResponse\n  | Heading3BlockObjectResponse\n  | BulletedListItemBlockObjectResponse\n  | NumberedListItemBlockObjectResponse\n  | QuoteBlockObjectResponse\n  | ToDoBlockObjectResponse\n  | ToggleBlockObjectResponse\n  | TemplateBlockObjectResponse\n  | SyncedBlockBlockObjectResponse\n  | ChildPageBlockObjectResponse\n  | ChildDatabaseBlockObjectResponse\n  | EquationBlockObjectResponse\n  | CodeBlockObjectResponse\n  | CalloutBlockObjectResponse\n  | DividerBlockObjectResponse\n  | BreadcrumbBlockObjectResponse\n  | TableOfContentsBlockObjectResponse\n  | ColumnListBlockObjectResponse\n  | ColumnBlockObjectResponse\n  | LinkToPageBlockObjectResponse\n  | TableBlockObjectResponse\n  | TableRowBlockObjectResponse\n  | EmbedBlockObjectResponse\n  | BookmarkBlockObjectResponse\n  | ImageBlockObjectResponse\n  | VideoBlockObjectResponse\n  | PdfBlockObjectResponse\n  | FileBlockObjectResponse\n  | AudioBlockObjectResponse\n  | LinkPreviewBlockObjectResponse\n  | UnsupportedBlockObjectResponse\n\nexport type NumberPropertyItemObjectResponse = {\n  type: \"number\"\n  number: number | null\n  object: \"property_item\"\n  id: string\n}\n\nexport type UrlPropertyItemObjectResponse = {\n  type: \"url\"\n  url: string | null\n  object: \"property_item\"\n  id: string\n}\n\nexport type SelectPropertyItemObjectResponse = {\n  type: \"select\"\n  select: PartialSelectResponse | null\n  object: \"property_item\"\n  id: string\n}\n\nexport type MultiSelectPropertyItemObjectResponse = {\n  type: \"multi_select\"\n  multi_select: Array<PartialSelectResponse>\n  object: \"property_item\"\n  id: string\n}\n\nexport type StatusPropertyItemObjectResponse = {\n  type: \"status\"\n  status: PartialSelectResponse | null\n  object: \"property_item\"\n  id: string\n}\n\nexport type DatePropertyItemObjectResponse = {\n  type: \"date\"\n  date: DateResponse | null\n  object: \"property_item\"\n  id: string\n}\n\nexport type EmailPropertyItemObjectResponse = {\n  type: \"email\"\n  email: string | null\n  object: \"property_item\"\n  id: string\n}\n\nexport type PhoneNumberPropertyItemObjectResponse = {\n  type: \"phone_number\"\n  phone_number: string | null\n  object: \"property_item\"\n  id: string\n}\n\nexport type CheckboxPropertyItemObjectResponse = {\n  type: \"checkbox\"\n  checkbox: boolean\n  object: \"property_item\"\n  id: string\n}\n\nexport type FilesPropertyItemObjectResponse = {\n  type: \"files\"\n  files: Array<\n    | {\n        file: { url: string; expiry_time: string }\n        name: StringRequest\n        type?: \"file\"\n      }\n    | { external: { url: TextRequest }; name: StringRequest; type?: \"external\" }\n  >\n  object: \"property_item\"\n  id: string\n}\n\nexport type CreatedByPropertyItemObjectResponse = {\n  type: \"created_by\"\n  created_by: PartialUserObjectResponse | UserObjectResponse\n  object: \"property_item\"\n  id: string\n}\n\nexport type CreatedTimePropertyItemObjectResponse = {\n  type: \"created_time\"\n  created_time: string\n  object: \"property_item\"\n  id: string\n}\n\nexport type LastEditedByPropertyItemObjectResponse = {\n  type: \"last_edited_by\"\n  last_edited_by: PartialUserObjectResponse | UserObjectResponse\n  object: \"property_item\"\n  id: string\n}\n\nexport type LastEditedTimePropertyItemObjectResponse = {\n  type: \"last_edited_time\"\n  last_edited_time: string\n  object: \"property_item\"\n  id: string\n}\n\nexport type FormulaPropertyItemObjectResponse = {\n  type: \"formula\"\n  formula: FormulaPropertyResponse\n  object: \"property_item\"\n  id: string\n}\n\nexport type ButtonPropertyItemObjectResponse = {\n  type: \"button\"\n  button: Record<string, never>\n  object: \"property_item\"\n  id: string\n}\n\nexport type UniqueIdPropertyItemObjectResponse = {\n  type: \"unique_id\"\n  unique_id: { prefix: string | null; number: number | null }\n  object: \"property_item\"\n  id: string\n}\n\nexport type VerificationPropertyItemObjectResponse = {\n  type: \"verification\"\n  verification:\n    | VerificationPropertyUnverifiedResponse\n    | null\n    | VerificationPropertyResponse\n    | null\n  object: \"property_item\"\n  id: string\n}\n\nexport type TitlePropertyItemObjectResponse = {\n  type: \"title\"\n  title: RichTextItemResponse\n  object: \"property_item\"\n  id: string\n}\n\nexport type RichTextPropertyItemObjectResponse = {\n  type: \"rich_text\"\n  rich_text: RichTextItemResponse\n  object: \"property_item\"\n  id: string\n}\n\nexport type PeoplePropertyItemObjectResponse = {\n  type: \"people\"\n  people: PartialUserObjectResponse | UserObjectResponse\n  object: \"property_item\"\n  id: string\n}\n\nexport type RelationPropertyItemObjectResponse = {\n  type: \"relation\"\n  relation: { id: string }\n  object: \"property_item\"\n  id: string\n}\n\nexport type RollupPropertyItemObjectResponse = {\n  type: \"rollup\"\n  rollup:\n    | { type: \"number\"; number: number | null; function: RollupFunction }\n    | { type: \"date\"; date: DateResponse | null; function: RollupFunction }\n    | { type: \"array\"; array: Array<EmptyObject>; function: RollupFunction }\n    | {\n        type: \"unsupported\"\n        unsupported: EmptyObject\n        function: RollupFunction\n      }\n    | { type: \"incomplete\"; incomplete: EmptyObject; function: RollupFunction }\n  object: \"property_item\"\n  id: string\n}\n\nexport type PropertyItemObjectResponse =\n  | NumberPropertyItemObjectResponse\n  | UrlPropertyItemObjectResponse\n  | SelectPropertyItemObjectResponse\n  | MultiSelectPropertyItemObjectResponse\n  | StatusPropertyItemObjectResponse\n  | DatePropertyItemObjectResponse\n  | EmailPropertyItemObjectResponse\n  | PhoneNumberPropertyItemObjectResponse\n  | CheckboxPropertyItemObjectResponse\n  | FilesPropertyItemObjectResponse\n  | CreatedByPropertyItemObjectResponse\n  | CreatedTimePropertyItemObjectResponse\n  | LastEditedByPropertyItemObjectResponse\n  | LastEditedTimePropertyItemObjectResponse\n  | FormulaPropertyItemObjectResponse\n  | ButtonPropertyItemObjectResponse\n  | UniqueIdPropertyItemObjectResponse\n  | VerificationPropertyItemObjectResponse\n  | TitlePropertyItemObjectResponse\n  | RichTextPropertyItemObjectResponse\n  | PeoplePropertyItemObjectResponse\n  | RelationPropertyItemObjectResponse\n  | RollupPropertyItemObjectResponse\n\nexport type CommentObjectResponse = {\n  object: \"comment\"\n  id: string\n  parent:\n    | { type: \"page_id\"; page_id: IdRequest }\n    | { type: \"block_id\"; block_id: IdRequest }\n  discussion_id: string\n  rich_text: Array<RichTextItemResponse>\n  created_by: PartialUserObjectResponse\n  created_time: string\n  last_edited_time: string\n}\n\nexport type PartialCommentObjectResponse = { object: \"comment\"; id: string }\n\nexport type PropertyItemPropertyItemListResponse = {\n  type: \"property_item\"\n  property_item:\n    | { type: \"title\"; title: EmptyObject; next_url: string | null; id: string }\n    | {\n        type: \"rich_text\"\n        rich_text: EmptyObject\n        next_url: string | null\n        id: string\n      }\n    | {\n        type: \"people\"\n        people: EmptyObject\n        next_url: string | null\n        id: string\n      }\n    | {\n        type: \"relation\"\n        relation: EmptyObject\n        next_url: string | null\n        id: string\n      }\n    | {\n        type: \"rollup\"\n        rollup:\n          | { type: \"number\"; number: number | null; function: RollupFunction }\n          | {\n              type: \"date\"\n              date: DateResponse | null\n              function: RollupFunction\n            }\n          | {\n              type: \"array\"\n              array: Array<EmptyObject>\n              function: RollupFunction\n            }\n          | {\n              type: \"unsupported\"\n              unsupported: EmptyObject\n              function: RollupFunction\n            }\n          | {\n              type: \"incomplete\"\n              incomplete: EmptyObject\n              function: RollupFunction\n            }\n        next_url: string | null\n        id: string\n      }\n  object: \"list\"\n  next_cursor: string | null\n  has_more: boolean\n  results: Array<PropertyItemObjectResponse>\n}\n\nexport type PropertyItemListResponse = PropertyItemPropertyItemListResponse\n\ntype DateRequest = {\n  start: string\n  end?: string | null\n  time_zone?: TimeZoneRequest | null\n}\n\ntype TemplateMentionRequest =\n  | { template_mention_date: \"today\" | \"now\"; type?: \"template_mention_date\" }\n  | { template_mention_user: \"me\"; type?: \"template_mention_user\" }\n\ntype RichTextItemRequest =\n  | {\n      text: { content: string; link?: { url: TextRequest } | null }\n      type?: \"text\"\n      annotations?: {\n        bold?: boolean\n        italic?: boolean\n        strikethrough?: boolean\n        underline?: boolean\n        code?: boolean\n        color?:\n          | \"default\"\n          | \"gray\"\n          | \"brown\"\n          | \"orange\"\n          | \"yellow\"\n          | \"green\"\n          | \"blue\"\n          | \"purple\"\n          | \"pink\"\n          | \"red\"\n          | \"default_background\"\n          | \"gray_background\"\n          | \"brown_background\"\n          | \"orange_background\"\n          | \"yellow_background\"\n          | \"green_background\"\n          | \"blue_background\"\n          | \"purple_background\"\n          | \"pink_background\"\n          | \"red_background\"\n      }\n    }\n  | {\n      mention:\n        | {\n            user:\n              | { id: IdRequest }\n              | {\n                  person: { email?: string }\n                  id: IdRequest\n                  type?: \"person\"\n                  name?: string | null\n                  avatar_url?: string | null\n                  object?: \"user\"\n                }\n              | {\n                  bot:\n                    | EmptyObject\n                    | {\n                        owner:\n                          | {\n                              type: \"user\"\n                              user:\n                                | {\n                                    type: \"person\"\n                                    person: { email: string }\n                                    name: string | null\n                                    avatar_url: string | null\n                                    id: IdRequest\n                                    object: \"user\"\n                                  }\n                                | PartialUserObjectResponse\n                            }\n                          | { type: \"workspace\"; workspace: true }\n                        workspace_name: string | null\n                      }\n                  id: IdRequest\n                  type?: \"bot\"\n                  name?: string | null\n                  avatar_url?: string | null\n                  object?: \"user\"\n                }\n          }\n        | { date: DateRequest }\n        | { page: { id: IdRequest } }\n        | { database: { id: IdRequest } }\n        | { template_mention: TemplateMentionRequest }\n        | { custom_emoji: { id: IdRequest; name?: string; url?: string } }\n      type?: \"mention\"\n      annotations?: {\n        bold?: boolean\n        italic?: boolean\n        strikethrough?: boolean\n        underline?: boolean\n        code?: boolean\n        color?:\n          | \"default\"\n          | \"gray\"\n          | \"brown\"\n          | \"orange\"\n          | \"yellow\"\n          | \"green\"\n          | \"blue\"\n          | \"purple\"\n          | \"pink\"\n          | \"red\"\n          | \"default_background\"\n          | \"gray_background\"\n          | \"brown_background\"\n          | \"orange_background\"\n          | \"yellow_background\"\n          | \"green_background\"\n          | \"blue_background\"\n          | \"purple_background\"\n          | \"pink_background\"\n          | \"red_background\"\n      }\n    }\n  | {\n      equation: { expression: TextRequest }\n      type?: \"equation\"\n      annotations?: {\n        bold?: boolean\n        italic?: boolean\n        strikethrough?: boolean\n        underline?: boolean\n        code?: boolean\n        color?:\n          | \"default\"\n          | \"gray\"\n          | \"brown\"\n          | \"orange\"\n          | \"yellow\"\n          | \"green\"\n          | \"blue\"\n          | \"purple\"\n          | \"pink\"\n          | \"red\"\n          | \"default_background\"\n          | \"gray_background\"\n          | \"brown_background\"\n          | \"orange_background\"\n          | \"yellow_background\"\n          | \"green_background\"\n          | \"blue_background\"\n          | \"purple_background\"\n          | \"pink_background\"\n          | \"red_background\"\n      }\n    }\n\nexport type BlockObjectRequestWithoutChildren =\n  | {\n      embed: { url: string; caption?: Array<RichTextItemRequest> }\n      type?: \"embed\"\n      object?: \"block\"\n    }\n  | {\n      bookmark: { url: string; caption?: Array<RichTextItemRequest> }\n      type?: \"bookmark\"\n      object?: \"block\"\n    }\n  | {\n      image: {\n        external: { url: TextRequest }\n        type?: \"external\"\n        caption?: Array<RichTextItemRequest>\n      }\n      type?: \"image\"\n      object?: \"block\"\n    }\n  | {\n      video: {\n        external: { url: TextRequest }\n        type?: \"external\"\n        caption?: Array<RichTextItemRequest>\n      }\n      type?: \"video\"\n      object?: \"block\"\n    }\n  | {\n      pdf: {\n        external: { url: TextRequest }\n        type?: \"external\"\n        caption?: Array<RichTextItemRequest>\n      }\n      type?: \"pdf\"\n      object?: \"block\"\n    }\n  | {\n      file: {\n        external: { url: TextRequest }\n        type?: \"external\"\n        caption?: Array<RichTextItemRequest>\n        name?: StringRequest\n      }\n      type?: \"file\"\n      object?: \"block\"\n    }\n  | {\n      audio: {\n        external: { url: TextRequest }\n        type?: \"external\"\n        caption?: Array<RichTextItemRequest>\n      }\n      type?: \"audio\"\n      object?: \"block\"\n    }\n  | {\n      code: {\n        rich_text: Array<RichTextItemRequest>\n        language: LanguageRequest\n        caption?: Array<RichTextItemRequest>\n      }\n      type?: \"code\"\n      object?: \"block\"\n    }\n  | { equation: { expression: string }; type?: \"equation\"; object?: \"block\" }\n  | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n  | { breadcrumb: EmptyObject; type?: \"breadcrumb\"; object?: \"block\" }\n  | {\n      table_of_contents: { color?: ApiColor }\n      type?: \"table_of_contents\"\n      object?: \"block\"\n    }\n  | {\n      link_to_page:\n        | { page_id: IdRequest; type?: \"page_id\" }\n        | { database_id: IdRequest; type?: \"database_id\" }\n        | { comment_id: IdRequest; type?: \"comment_id\" }\n      type?: \"link_to_page\"\n      object?: \"block\"\n    }\n  | {\n      table_row: { cells: Array<Array<RichTextItemRequest>> }\n      type?: \"table_row\"\n      object?: \"block\"\n    }\n  | {\n      heading_1: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        is_toggleable?: boolean\n      }\n      type?: \"heading_1\"\n      object?: \"block\"\n    }\n  | {\n      heading_2: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        is_toggleable?: boolean\n      }\n      type?: \"heading_2\"\n      object?: \"block\"\n    }\n  | {\n      heading_3: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        is_toggleable?: boolean\n      }\n      type?: \"heading_3\"\n      object?: \"block\"\n    }\n  | {\n      paragraph: { rich_text: Array<RichTextItemRequest>; color?: ApiColor }\n      type?: \"paragraph\"\n      object?: \"block\"\n    }\n  | {\n      bulleted_list_item: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n      }\n      type?: \"bulleted_list_item\"\n      object?: \"block\"\n    }\n  | {\n      numbered_list_item: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n      }\n      type?: \"numbered_list_item\"\n      object?: \"block\"\n    }\n  | {\n      quote: { rich_text: Array<RichTextItemRequest>; color?: ApiColor }\n      type?: \"quote\"\n      object?: \"block\"\n    }\n  | {\n      to_do: {\n        rich_text: Array<RichTextItemRequest>\n        checked?: boolean\n        color?: ApiColor\n      }\n      type?: \"to_do\"\n      object?: \"block\"\n    }\n  | {\n      toggle: { rich_text: Array<RichTextItemRequest>; color?: ApiColor }\n      type?: \"toggle\"\n      object?: \"block\"\n    }\n  | {\n      template: { rich_text: Array<RichTextItemRequest> }\n      type?: \"template\"\n      object?: \"block\"\n    }\n  | {\n      callout: {\n        rich_text: Array<RichTextItemRequest>\n        icon?:\n          | { emoji: EmojiRequest; type?: \"emoji\" }\n          | { external: { url: TextRequest }; type?: \"external\" }\n          | {\n              custom_emoji: { id: IdRequest; name?: string; url?: string }\n              type?: \"custom_emoji\"\n            }\n        color?: ApiColor\n      }\n      type?: \"callout\"\n      object?: \"block\"\n    }\n  | {\n      synced_block: {\n        synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n      }\n      type?: \"synced_block\"\n      object?: \"block\"\n    }\n\nexport type BlockObjectRequest =\n  | {\n      embed: { url: string; caption?: Array<RichTextItemRequest> }\n      type?: \"embed\"\n      object?: \"block\"\n    }\n  | {\n      bookmark: { url: string; caption?: Array<RichTextItemRequest> }\n      type?: \"bookmark\"\n      object?: \"block\"\n    }\n  | {\n      image: {\n        external: { url: TextRequest }\n        type?: \"external\"\n        caption?: Array<RichTextItemRequest>\n      }\n      type?: \"image\"\n      object?: \"block\"\n    }\n  | {\n      video: {\n        external: { url: TextRequest }\n        type?: \"external\"\n        caption?: Array<RichTextItemRequest>\n      }\n      type?: \"video\"\n      object?: \"block\"\n    }\n  | {\n      pdf: {\n        external: { url: TextRequest }\n        type?: \"external\"\n        caption?: Array<RichTextItemRequest>\n      }\n      type?: \"pdf\"\n      object?: \"block\"\n    }\n  | {\n      file: {\n        external: { url: TextRequest }\n        type?: \"external\"\n        caption?: Array<RichTextItemRequest>\n        name?: StringRequest\n      }\n      type?: \"file\"\n      object?: \"block\"\n    }\n  | {\n      audio: {\n        external: { url: TextRequest }\n        type?: \"external\"\n        caption?: Array<RichTextItemRequest>\n      }\n      type?: \"audio\"\n      object?: \"block\"\n    }\n  | {\n      code: {\n        rich_text: Array<RichTextItemRequest>\n        language: LanguageRequest\n        caption?: Array<RichTextItemRequest>\n      }\n      type?: \"code\"\n      object?: \"block\"\n    }\n  | { equation: { expression: string }; type?: \"equation\"; object?: \"block\" }\n  | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n  | { breadcrumb: EmptyObject; type?: \"breadcrumb\"; object?: \"block\" }\n  | {\n      table_of_contents: { color?: ApiColor }\n      type?: \"table_of_contents\"\n      object?: \"block\"\n    }\n  | {\n      link_to_page:\n        | { page_id: IdRequest; type?: \"page_id\" }\n        | { database_id: IdRequest; type?: \"database_id\" }\n        | { comment_id: IdRequest; type?: \"comment_id\" }\n      type?: \"link_to_page\"\n      object?: \"block\"\n    }\n  | {\n      table_row: { cells: Array<Array<RichTextItemRequest>> }\n      type?: \"table_row\"\n      object?: \"block\"\n    }\n  | {\n      table: {\n        table_width: number\n        children: Array<{\n          table_row: { cells: Array<Array<RichTextItemRequest>> }\n          type?: \"table_row\"\n          object?: \"block\"\n        }>\n        has_column_header?: boolean\n        has_row_header?: boolean\n      }\n      type?: \"table\"\n      object?: \"block\"\n    }\n  | {\n      column_list: {\n        children: Array<{\n          column: {\n            children: Array<\n              | {\n                  embed: { url: string; caption?: Array<RichTextItemRequest> }\n                  type?: \"embed\"\n                  object?: \"block\"\n                }\n              | {\n                  bookmark: {\n                    url: string\n                    caption?: Array<RichTextItemRequest>\n                  }\n                  type?: \"bookmark\"\n                  object?: \"block\"\n                }\n              | {\n                  image: {\n                    external: { url: TextRequest }\n                    type?: \"external\"\n                    caption?: Array<RichTextItemRequest>\n                  }\n                  type?: \"image\"\n                  object?: \"block\"\n                }\n              | {\n                  video: {\n                    external: { url: TextRequest }\n                    type?: \"external\"\n                    caption?: Array<RichTextItemRequest>\n                  }\n                  type?: \"video\"\n                  object?: \"block\"\n                }\n              | {\n                  pdf: {\n                    external: { url: TextRequest }\n                    type?: \"external\"\n                    caption?: Array<RichTextItemRequest>\n                  }\n                  type?: \"pdf\"\n                  object?: \"block\"\n                }\n              | {\n                  file: {\n                    external: { url: TextRequest }\n                    type?: \"external\"\n                    caption?: Array<RichTextItemRequest>\n                    name?: StringRequest\n                  }\n                  type?: \"file\"\n                  object?: \"block\"\n                }\n              | {\n                  audio: {\n                    external: { url: TextRequest }\n                    type?: \"external\"\n                    caption?: Array<RichTextItemRequest>\n                  }\n                  type?: \"audio\"\n                  object?: \"block\"\n                }\n              | {\n                  code: {\n                    rich_text: Array<RichTextItemRequest>\n                    language: LanguageRequest\n                    caption?: Array<RichTextItemRequest>\n                  }\n                  type?: \"code\"\n                  object?: \"block\"\n                }\n              | {\n                  equation: { expression: string }\n                  type?: \"equation\"\n                  object?: \"block\"\n                }\n              | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n              | {\n                  breadcrumb: EmptyObject\n                  type?: \"breadcrumb\"\n                  object?: \"block\"\n                }\n              | {\n                  table_of_contents: { color?: ApiColor }\n                  type?: \"table_of_contents\"\n                  object?: \"block\"\n                }\n              | {\n                  link_to_page:\n                    | { page_id: IdRequest; type?: \"page_id\" }\n                    | { database_id: IdRequest; type?: \"database_id\" }\n                    | { comment_id: IdRequest; type?: \"comment_id\" }\n                  type?: \"link_to_page\"\n                  object?: \"block\"\n                }\n              | {\n                  table_row: { cells: Array<Array<RichTextItemRequest>> }\n                  type?: \"table_row\"\n                  object?: \"block\"\n                }\n              | {\n                  heading_1: {\n                    rich_text: Array<RichTextItemRequest>\n                    color?: ApiColor\n                    is_toggleable?: boolean\n                    children?: Array<BlockObjectRequestWithoutChildren>\n                  }\n                  type?: \"heading_1\"\n                  object?: \"block\"\n                }\n              | {\n                  heading_2: {\n                    rich_text: Array<RichTextItemRequest>\n                    color?: ApiColor\n                    is_toggleable?: boolean\n                    children?: Array<BlockObjectRequestWithoutChildren>\n                  }\n                  type?: \"heading_2\"\n                  object?: \"block\"\n                }\n              | {\n                  heading_3: {\n                    rich_text: Array<RichTextItemRequest>\n                    color?: ApiColor\n                    is_toggleable?: boolean\n                    children?: Array<BlockObjectRequestWithoutChildren>\n                  }\n                  type?: \"heading_3\"\n                  object?: \"block\"\n                }\n              | {\n                  paragraph: {\n                    rich_text: Array<RichTextItemRequest>\n                    color?: ApiColor\n                    children?: Array<BlockObjectRequestWithoutChildren>\n                  }\n                  type?: \"paragraph\"\n                  object?: \"block\"\n                }\n              | {\n                  bulleted_list_item: {\n                    rich_text: Array<RichTextItemRequest>\n                    color?: ApiColor\n                    children?: Array<BlockObjectRequestWithoutChildren>\n                  }\n                  type?: \"bulleted_list_item\"\n                  object?: \"block\"\n                }\n              | {\n                  numbered_list_item: {\n                    rich_text: Array<RichTextItemRequest>\n                    color?: ApiColor\n                    children?: Array<BlockObjectRequestWithoutChildren>\n                  }\n                  type?: \"numbered_list_item\"\n                  object?: \"block\"\n                }\n              | {\n                  quote: {\n                    rich_text: Array<RichTextItemRequest>\n                    color?: ApiColor\n                    children?: Array<BlockObjectRequestWithoutChildren>\n                  }\n                  type?: \"quote\"\n                  object?: \"block\"\n                }\n              | {\n                  table: {\n                    table_width: number\n                    children: Array<{\n                      table_row: { cells: Array<Array<RichTextItemRequest>> }\n                      type?: \"table_row\"\n                      object?: \"block\"\n                    }>\n                    has_column_header?: boolean\n                    has_row_header?: boolean\n                  }\n                  type?: \"table\"\n                  object?: \"block\"\n                }\n              | {\n                  to_do: {\n                    rich_text: Array<RichTextItemRequest>\n                    color?: ApiColor\n                    children?: Array<BlockObjectRequestWithoutChildren>\n                    checked?: boolean\n                  }\n                  type?: \"to_do\"\n                  object?: \"block\"\n                }\n              | {\n                  toggle: {\n                    rich_text: Array<RichTextItemRequest>\n                    color?: ApiColor\n                    children?: Array<BlockObjectRequestWithoutChildren>\n                  }\n                  type?: \"toggle\"\n                  object?: \"block\"\n                }\n              | {\n                  template: {\n                    rich_text: Array<RichTextItemRequest>\n                    children?: Array<BlockObjectRequestWithoutChildren>\n                  }\n                  type?: \"template\"\n                  object?: \"block\"\n                }\n              | {\n                  callout: {\n                    rich_text: Array<RichTextItemRequest>\n                    color?: ApiColor\n                    children?: Array<BlockObjectRequestWithoutChildren>\n                    icon?:\n                      | { emoji: EmojiRequest; type?: \"emoji\" }\n                      | { external: { url: TextRequest }; type?: \"external\" }\n                      | {\n                          custom_emoji: {\n                            id: IdRequest\n                            name?: string\n                            url?: string\n                          }\n                          type?: \"custom_emoji\"\n                        }\n                  }\n                  type?: \"callout\"\n                  object?: \"block\"\n                }\n              | {\n                  synced_block: {\n                    synced_from: {\n                      block_id: IdRequest\n                      type?: \"block_id\"\n                    } | null\n                    children?: Array<BlockObjectRequestWithoutChildren>\n                  }\n                  type?: \"synced_block\"\n                  object?: \"block\"\n                }\n            >\n          }\n          type?: \"column\"\n          object?: \"block\"\n        }>\n      }\n      type?: \"column_list\"\n      object?: \"block\"\n    }\n  | {\n      column: {\n        children: Array<\n          | {\n              embed: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"embed\"\n              object?: \"block\"\n            }\n          | {\n              bookmark: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"bookmark\"\n              object?: \"block\"\n            }\n          | {\n              image: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"image\"\n              object?: \"block\"\n            }\n          | {\n              video: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"video\"\n              object?: \"block\"\n            }\n          | {\n              pdf: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"pdf\"\n              object?: \"block\"\n            }\n          | {\n              file: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n                name?: StringRequest\n              }\n              type?: \"file\"\n              object?: \"block\"\n            }\n          | {\n              audio: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"audio\"\n              object?: \"block\"\n            }\n          | {\n              code: {\n                rich_text: Array<RichTextItemRequest>\n                language: LanguageRequest\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"code\"\n              object?: \"block\"\n            }\n          | {\n              equation: { expression: string }\n              type?: \"equation\"\n              object?: \"block\"\n            }\n          | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n          | { breadcrumb: EmptyObject; type?: \"breadcrumb\"; object?: \"block\" }\n          | {\n              table_of_contents: { color?: ApiColor }\n              type?: \"table_of_contents\"\n              object?: \"block\"\n            }\n          | {\n              link_to_page:\n                | { page_id: IdRequest; type?: \"page_id\" }\n                | { database_id: IdRequest; type?: \"database_id\" }\n                | { comment_id: IdRequest; type?: \"comment_id\" }\n              type?: \"link_to_page\"\n              object?: \"block\"\n            }\n          | {\n              table_row: { cells: Array<Array<RichTextItemRequest>> }\n              type?: \"table_row\"\n              object?: \"block\"\n            }\n          | {\n              heading_1: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_1\"\n              object?: \"block\"\n            }\n          | {\n              heading_2: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_2\"\n              object?: \"block\"\n            }\n          | {\n              heading_3: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_3\"\n              object?: \"block\"\n            }\n          | {\n              paragraph: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"paragraph\"\n              object?: \"block\"\n            }\n          | {\n              bulleted_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"bulleted_list_item\"\n              object?: \"block\"\n            }\n          | {\n              numbered_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"numbered_list_item\"\n              object?: \"block\"\n            }\n          | {\n              quote: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"quote\"\n              object?: \"block\"\n            }\n          | {\n              table: {\n                table_width: number\n                children: Array<{\n                  table_row: { cells: Array<Array<RichTextItemRequest>> }\n                  type?: \"table_row\"\n                  object?: \"block\"\n                }>\n                has_column_header?: boolean\n                has_row_header?: boolean\n              }\n              type?: \"table\"\n              object?: \"block\"\n            }\n          | {\n              to_do: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                checked?: boolean\n              }\n              type?: \"to_do\"\n              object?: \"block\"\n            }\n          | {\n              toggle: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"toggle\"\n              object?: \"block\"\n            }\n          | {\n              template: {\n                rich_text: Array<RichTextItemRequest>\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"template\"\n              object?: \"block\"\n            }\n          | {\n              callout: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                icon?:\n                  | { emoji: EmojiRequest; type?: \"emoji\" }\n                  | { external: { url: TextRequest }; type?: \"external\" }\n                  | {\n                      custom_emoji: {\n                        id: IdRequest\n                        name?: string\n                        url?: string\n                      }\n                      type?: \"custom_emoji\"\n                    }\n              }\n              type?: \"callout\"\n              object?: \"block\"\n            }\n          | {\n              synced_block: {\n                synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"synced_block\"\n              object?: \"block\"\n            }\n        >\n      }\n      type?: \"column\"\n      object?: \"block\"\n    }\n  | {\n      heading_1: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        is_toggleable?: boolean\n        children?: Array<\n          | {\n              embed: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"embed\"\n              object?: \"block\"\n            }\n          | {\n              bookmark: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"bookmark\"\n              object?: \"block\"\n            }\n          | {\n              image: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"image\"\n              object?: \"block\"\n            }\n          | {\n              video: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"video\"\n              object?: \"block\"\n            }\n          | {\n              pdf: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"pdf\"\n              object?: \"block\"\n            }\n          | {\n              file: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n                name?: StringRequest\n              }\n              type?: \"file\"\n              object?: \"block\"\n            }\n          | {\n              audio: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"audio\"\n              object?: \"block\"\n            }\n          | {\n              code: {\n                rich_text: Array<RichTextItemRequest>\n                language: LanguageRequest\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"code\"\n              object?: \"block\"\n            }\n          | {\n              equation: { expression: string }\n              type?: \"equation\"\n              object?: \"block\"\n            }\n          | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n          | { breadcrumb: EmptyObject; type?: \"breadcrumb\"; object?: \"block\" }\n          | {\n              table_of_contents: { color?: ApiColor }\n              type?: \"table_of_contents\"\n              object?: \"block\"\n            }\n          | {\n              link_to_page:\n                | { page_id: IdRequest; type?: \"page_id\" }\n                | { database_id: IdRequest; type?: \"database_id\" }\n                | { comment_id: IdRequest; type?: \"comment_id\" }\n              type?: \"link_to_page\"\n              object?: \"block\"\n            }\n          | {\n              table_row: { cells: Array<Array<RichTextItemRequest>> }\n              type?: \"table_row\"\n              object?: \"block\"\n            }\n          | {\n              heading_1: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_1\"\n              object?: \"block\"\n            }\n          | {\n              heading_2: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_2\"\n              object?: \"block\"\n            }\n          | {\n              heading_3: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_3\"\n              object?: \"block\"\n            }\n          | {\n              paragraph: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"paragraph\"\n              object?: \"block\"\n            }\n          | {\n              bulleted_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"bulleted_list_item\"\n              object?: \"block\"\n            }\n          | {\n              numbered_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"numbered_list_item\"\n              object?: \"block\"\n            }\n          | {\n              quote: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"quote\"\n              object?: \"block\"\n            }\n          | {\n              table: {\n                table_width: number\n                children: Array<{\n                  table_row: { cells: Array<Array<RichTextItemRequest>> }\n                  type?: \"table_row\"\n                  object?: \"block\"\n                }>\n                has_column_header?: boolean\n                has_row_header?: boolean\n              }\n              type?: \"table\"\n              object?: \"block\"\n            }\n          | {\n              to_do: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                checked?: boolean\n              }\n              type?: \"to_do\"\n              object?: \"block\"\n            }\n          | {\n              toggle: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"toggle\"\n              object?: \"block\"\n            }\n          | {\n              template: {\n                rich_text: Array<RichTextItemRequest>\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"template\"\n              object?: \"block\"\n            }\n          | {\n              callout: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                icon?:\n                  | { emoji: EmojiRequest; type?: \"emoji\" }\n                  | { external: { url: TextRequest }; type?: \"external\" }\n                  | {\n                      custom_emoji: {\n                        id: IdRequest\n                        name?: string\n                        url?: string\n                      }\n                      type?: \"custom_emoji\"\n                    }\n              }\n              type?: \"callout\"\n              object?: \"block\"\n            }\n          | {\n              synced_block: {\n                synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"synced_block\"\n              object?: \"block\"\n            }\n        >\n      }\n      type?: \"heading_1\"\n      object?: \"block\"\n    }\n  | {\n      heading_2: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        is_toggleable?: boolean\n        children?: Array<\n          | {\n              embed: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"embed\"\n              object?: \"block\"\n            }\n          | {\n              bookmark: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"bookmark\"\n              object?: \"block\"\n            }\n          | {\n              image: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"image\"\n              object?: \"block\"\n            }\n          | {\n              video: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"video\"\n              object?: \"block\"\n            }\n          | {\n              pdf: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"pdf\"\n              object?: \"block\"\n            }\n          | {\n              file: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n                name?: StringRequest\n              }\n              type?: \"file\"\n              object?: \"block\"\n            }\n          | {\n              audio: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"audio\"\n              object?: \"block\"\n            }\n          | {\n              code: {\n                rich_text: Array<RichTextItemRequest>\n                language: LanguageRequest\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"code\"\n              object?: \"block\"\n            }\n          | {\n              equation: { expression: string }\n              type?: \"equation\"\n              object?: \"block\"\n            }\n          | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n          | { breadcrumb: EmptyObject; type?: \"breadcrumb\"; object?: \"block\" }\n          | {\n              table_of_contents: { color?: ApiColor }\n              type?: \"table_of_contents\"\n              object?: \"block\"\n            }\n          | {\n              link_to_page:\n                | { page_id: IdRequest; type?: \"page_id\" }\n                | { database_id: IdRequest; type?: \"database_id\" }\n                | { comment_id: IdRequest; type?: \"comment_id\" }\n              type?: \"link_to_page\"\n              object?: \"block\"\n            }\n          | {\n              table_row: { cells: Array<Array<RichTextItemRequest>> }\n              type?: \"table_row\"\n              object?: \"block\"\n            }\n          | {\n              heading_1: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_1\"\n              object?: \"block\"\n            }\n          | {\n              heading_2: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_2\"\n              object?: \"block\"\n            }\n          | {\n              heading_3: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_3\"\n              object?: \"block\"\n            }\n          | {\n              paragraph: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"paragraph\"\n              object?: \"block\"\n            }\n          | {\n              bulleted_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"bulleted_list_item\"\n              object?: \"block\"\n            }\n          | {\n              numbered_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"numbered_list_item\"\n              object?: \"block\"\n            }\n          | {\n              quote: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"quote\"\n              object?: \"block\"\n            }\n          | {\n              table: {\n                table_width: number\n                children: Array<{\n                  table_row: { cells: Array<Array<RichTextItemRequest>> }\n                  type?: \"table_row\"\n                  object?: \"block\"\n                }>\n                has_column_header?: boolean\n                has_row_header?: boolean\n              }\n              type?: \"table\"\n              object?: \"block\"\n            }\n          | {\n              to_do: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                checked?: boolean\n              }\n              type?: \"to_do\"\n              object?: \"block\"\n            }\n          | {\n              toggle: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"toggle\"\n              object?: \"block\"\n            }\n          | {\n              template: {\n                rich_text: Array<RichTextItemRequest>\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"template\"\n              object?: \"block\"\n            }\n          | {\n              callout: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                icon?:\n                  | { emoji: EmojiRequest; type?: \"emoji\" }\n                  | { external: { url: TextRequest }; type?: \"external\" }\n                  | {\n                      custom_emoji: {\n                        id: IdRequest\n                        name?: string\n                        url?: string\n                      }\n                      type?: \"custom_emoji\"\n                    }\n              }\n              type?: \"callout\"\n              object?: \"block\"\n            }\n          | {\n              synced_block: {\n                synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"synced_block\"\n              object?: \"block\"\n            }\n        >\n      }\n      type?: \"heading_2\"\n      object?: \"block\"\n    }\n  | {\n      heading_3: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        is_toggleable?: boolean\n        children?: Array<\n          | {\n              embed: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"embed\"\n              object?: \"block\"\n            }\n          | {\n              bookmark: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"bookmark\"\n              object?: \"block\"\n            }\n          | {\n              image: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"image\"\n              object?: \"block\"\n            }\n          | {\n              video: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"video\"\n              object?: \"block\"\n            }\n          | {\n              pdf: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"pdf\"\n              object?: \"block\"\n            }\n          | {\n              file: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n                name?: StringRequest\n              }\n              type?: \"file\"\n              object?: \"block\"\n            }\n          | {\n              audio: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"audio\"\n              object?: \"block\"\n            }\n          | {\n              code: {\n                rich_text: Array<RichTextItemRequest>\n                language: LanguageRequest\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"code\"\n              object?: \"block\"\n            }\n          | {\n              equation: { expression: string }\n              type?: \"equation\"\n              object?: \"block\"\n            }\n          | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n          | { breadcrumb: EmptyObject; type?: \"breadcrumb\"; object?: \"block\" }\n          | {\n              table_of_contents: { color?: ApiColor }\n              type?: \"table_of_contents\"\n              object?: \"block\"\n            }\n          | {\n              link_to_page:\n                | { page_id: IdRequest; type?: \"page_id\" }\n                | { database_id: IdRequest; type?: \"database_id\" }\n                | { comment_id: IdRequest; type?: \"comment_id\" }\n              type?: \"link_to_page\"\n              object?: \"block\"\n            }\n          | {\n              table_row: { cells: Array<Array<RichTextItemRequest>> }\n              type?: \"table_row\"\n              object?: \"block\"\n            }\n          | {\n              heading_1: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_1\"\n              object?: \"block\"\n            }\n          | {\n              heading_2: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_2\"\n              object?: \"block\"\n            }\n          | {\n              heading_3: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_3\"\n              object?: \"block\"\n            }\n          | {\n              paragraph: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"paragraph\"\n              object?: \"block\"\n            }\n          | {\n              bulleted_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"bulleted_list_item\"\n              object?: \"block\"\n            }\n          | {\n              numbered_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"numbered_list_item\"\n              object?: \"block\"\n            }\n          | {\n              quote: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"quote\"\n              object?: \"block\"\n            }\n          | {\n              table: {\n                table_width: number\n                children: Array<{\n                  table_row: { cells: Array<Array<RichTextItemRequest>> }\n                  type?: \"table_row\"\n                  object?: \"block\"\n                }>\n                has_column_header?: boolean\n                has_row_header?: boolean\n              }\n              type?: \"table\"\n              object?: \"block\"\n            }\n          | {\n              to_do: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                checked?: boolean\n              }\n              type?: \"to_do\"\n              object?: \"block\"\n            }\n          | {\n              toggle: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"toggle\"\n              object?: \"block\"\n            }\n          | {\n              template: {\n                rich_text: Array<RichTextItemRequest>\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"template\"\n              object?: \"block\"\n            }\n          | {\n              callout: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                icon?:\n                  | { emoji: EmojiRequest; type?: \"emoji\" }\n                  | { external: { url: TextRequest }; type?: \"external\" }\n                  | {\n                      custom_emoji: {\n                        id: IdRequest\n                        name?: string\n                        url?: string\n                      }\n                      type?: \"custom_emoji\"\n                    }\n              }\n              type?: \"callout\"\n              object?: \"block\"\n            }\n          | {\n              synced_block: {\n                synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"synced_block\"\n              object?: \"block\"\n            }\n        >\n      }\n      type?: \"heading_3\"\n      object?: \"block\"\n    }\n  | {\n      paragraph: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        children?: Array<\n          | {\n              embed: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"embed\"\n              object?: \"block\"\n            }\n          | {\n              bookmark: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"bookmark\"\n              object?: \"block\"\n            }\n          | {\n              image: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"image\"\n              object?: \"block\"\n            }\n          | {\n              video: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"video\"\n              object?: \"block\"\n            }\n          | {\n              pdf: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"pdf\"\n              object?: \"block\"\n            }\n          | {\n              file: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n                name?: StringRequest\n              }\n              type?: \"file\"\n              object?: \"block\"\n            }\n          | {\n              audio: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"audio\"\n              object?: \"block\"\n            }\n          | {\n              code: {\n                rich_text: Array<RichTextItemRequest>\n                language: LanguageRequest\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"code\"\n              object?: \"block\"\n            }\n          | {\n              equation: { expression: string }\n              type?: \"equation\"\n              object?: \"block\"\n            }\n          | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n          | { breadcrumb: EmptyObject; type?: \"breadcrumb\"; object?: \"block\" }\n          | {\n              table_of_contents: { color?: ApiColor }\n              type?: \"table_of_contents\"\n              object?: \"block\"\n            }\n          | {\n              link_to_page:\n                | { page_id: IdRequest; type?: \"page_id\" }\n                | { database_id: IdRequest; type?: \"database_id\" }\n                | { comment_id: IdRequest; type?: \"comment_id\" }\n              type?: \"link_to_page\"\n              object?: \"block\"\n            }\n          | {\n              table_row: { cells: Array<Array<RichTextItemRequest>> }\n              type?: \"table_row\"\n              object?: \"block\"\n            }\n          | {\n              heading_1: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_1\"\n              object?: \"block\"\n            }\n          | {\n              heading_2: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_2\"\n              object?: \"block\"\n            }\n          | {\n              heading_3: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_3\"\n              object?: \"block\"\n            }\n          | {\n              paragraph: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"paragraph\"\n              object?: \"block\"\n            }\n          | {\n              bulleted_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"bulleted_list_item\"\n              object?: \"block\"\n            }\n          | {\n              numbered_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"numbered_list_item\"\n              object?: \"block\"\n            }\n          | {\n              quote: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"quote\"\n              object?: \"block\"\n            }\n          | {\n              table: {\n                table_width: number\n                children: Array<{\n                  table_row: { cells: Array<Array<RichTextItemRequest>> }\n                  type?: \"table_row\"\n                  object?: \"block\"\n                }>\n                has_column_header?: boolean\n                has_row_header?: boolean\n              }\n              type?: \"table\"\n              object?: \"block\"\n            }\n          | {\n              to_do: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                checked?: boolean\n              }\n              type?: \"to_do\"\n              object?: \"block\"\n            }\n          | {\n              toggle: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"toggle\"\n              object?: \"block\"\n            }\n          | {\n              template: {\n                rich_text: Array<RichTextItemRequest>\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"template\"\n              object?: \"block\"\n            }\n          | {\n              callout: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                icon?:\n                  | { emoji: EmojiRequest; type?: \"emoji\" }\n                  | { external: { url: TextRequest }; type?: \"external\" }\n                  | {\n                      custom_emoji: {\n                        id: IdRequest\n                        name?: string\n                        url?: string\n                      }\n                      type?: \"custom_emoji\"\n                    }\n              }\n              type?: \"callout\"\n              object?: \"block\"\n            }\n          | {\n              synced_block: {\n                synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"synced_block\"\n              object?: \"block\"\n            }\n        >\n      }\n      type?: \"paragraph\"\n      object?: \"block\"\n    }\n  | {\n      bulleted_list_item: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        children?: Array<\n          | {\n              embed: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"embed\"\n              object?: \"block\"\n            }\n          | {\n              bookmark: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"bookmark\"\n              object?: \"block\"\n            }\n          | {\n              image: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"image\"\n              object?: \"block\"\n            }\n          | {\n              video: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"video\"\n              object?: \"block\"\n            }\n          | {\n              pdf: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"pdf\"\n              object?: \"block\"\n            }\n          | {\n              file: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n                name?: StringRequest\n              }\n              type?: \"file\"\n              object?: \"block\"\n            }\n          | {\n              audio: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"audio\"\n              object?: \"block\"\n            }\n          | {\n              code: {\n                rich_text: Array<RichTextItemRequest>\n                language: LanguageRequest\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"code\"\n              object?: \"block\"\n            }\n          | {\n              equation: { expression: string }\n              type?: \"equation\"\n              object?: \"block\"\n            }\n          | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n          | { breadcrumb: EmptyObject; type?: \"breadcrumb\"; object?: \"block\" }\n          | {\n              table_of_contents: { color?: ApiColor }\n              type?: \"table_of_contents\"\n              object?: \"block\"\n            }\n          | {\n              link_to_page:\n                | { page_id: IdRequest; type?: \"page_id\" }\n                | { database_id: IdRequest; type?: \"database_id\" }\n                | { comment_id: IdRequest; type?: \"comment_id\" }\n              type?: \"link_to_page\"\n              object?: \"block\"\n            }\n          | {\n              table_row: { cells: Array<Array<RichTextItemRequest>> }\n              type?: \"table_row\"\n              object?: \"block\"\n            }\n          | {\n              heading_1: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_1\"\n              object?: \"block\"\n            }\n          | {\n              heading_2: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_2\"\n              object?: \"block\"\n            }\n          | {\n              heading_3: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_3\"\n              object?: \"block\"\n            }\n          | {\n              paragraph: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"paragraph\"\n              object?: \"block\"\n            }\n          | {\n              bulleted_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"bulleted_list_item\"\n              object?: \"block\"\n            }\n          | {\n              numbered_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"numbered_list_item\"\n              object?: \"block\"\n            }\n          | {\n              quote: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"quote\"\n              object?: \"block\"\n            }\n          | {\n              table: {\n                table_width: number\n                children: Array<{\n                  table_row: { cells: Array<Array<RichTextItemRequest>> }\n                  type?: \"table_row\"\n                  object?: \"block\"\n                }>\n                has_column_header?: boolean\n                has_row_header?: boolean\n              }\n              type?: \"table\"\n              object?: \"block\"\n            }\n          | {\n              to_do: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                checked?: boolean\n              }\n              type?: \"to_do\"\n              object?: \"block\"\n            }\n          | {\n              toggle: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"toggle\"\n              object?: \"block\"\n            }\n          | {\n              template: {\n                rich_text: Array<RichTextItemRequest>\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"template\"\n              object?: \"block\"\n            }\n          | {\n              callout: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                icon?:\n                  | { emoji: EmojiRequest; type?: \"emoji\" }\n                  | { external: { url: TextRequest }; type?: \"external\" }\n                  | {\n                      custom_emoji: {\n                        id: IdRequest\n                        name?: string\n                        url?: string\n                      }\n                      type?: \"custom_emoji\"\n                    }\n              }\n              type?: \"callout\"\n              object?: \"block\"\n            }\n          | {\n              synced_block: {\n                synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"synced_block\"\n              object?: \"block\"\n            }\n        >\n      }\n      type?: \"bulleted_list_item\"\n      object?: \"block\"\n    }\n  | {\n      numbered_list_item: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        children?: Array<\n          | {\n              embed: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"embed\"\n              object?: \"block\"\n            }\n          | {\n              bookmark: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"bookmark\"\n              object?: \"block\"\n            }\n          | {\n              image: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"image\"\n              object?: \"block\"\n            }\n          | {\n              video: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"video\"\n              object?: \"block\"\n            }\n          | {\n              pdf: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"pdf\"\n              object?: \"block\"\n            }\n          | {\n              file: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n                name?: StringRequest\n              }\n              type?: \"file\"\n              object?: \"block\"\n            }\n          | {\n              audio: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"audio\"\n              object?: \"block\"\n            }\n          | {\n              code: {\n                rich_text: Array<RichTextItemRequest>\n                language: LanguageRequest\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"code\"\n              object?: \"block\"\n            }\n          | {\n              equation: { expression: string }\n              type?: \"equation\"\n              object?: \"block\"\n            }\n          | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n          | { breadcrumb: EmptyObject; type?: \"breadcrumb\"; object?: \"block\" }\n          | {\n              table_of_contents: { color?: ApiColor }\n              type?: \"table_of_contents\"\n              object?: \"block\"\n            }\n          | {\n              link_to_page:\n                | { page_id: IdRequest; type?: \"page_id\" }\n                | { database_id: IdRequest; type?: \"database_id\" }\n                | { comment_id: IdRequest; type?: \"comment_id\" }\n              type?: \"link_to_page\"\n              object?: \"block\"\n            }\n          | {\n              table_row: { cells: Array<Array<RichTextItemRequest>> }\n              type?: \"table_row\"\n              object?: \"block\"\n            }\n          | {\n              heading_1: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_1\"\n              object?: \"block\"\n            }\n          | {\n              heading_2: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_2\"\n              object?: \"block\"\n            }\n          | {\n              heading_3: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_3\"\n              object?: \"block\"\n            }\n          | {\n              paragraph: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"paragraph\"\n              object?: \"block\"\n            }\n          | {\n              bulleted_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"bulleted_list_item\"\n              object?: \"block\"\n            }\n          | {\n              numbered_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"numbered_list_item\"\n              object?: \"block\"\n            }\n          | {\n              quote: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"quote\"\n              object?: \"block\"\n            }\n          | {\n              table: {\n                table_width: number\n                children: Array<{\n                  table_row: { cells: Array<Array<RichTextItemRequest>> }\n                  type?: \"table_row\"\n                  object?: \"block\"\n                }>\n                has_column_header?: boolean\n                has_row_header?: boolean\n              }\n              type?: \"table\"\n              object?: \"block\"\n            }\n          | {\n              to_do: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                checked?: boolean\n              }\n              type?: \"to_do\"\n              object?: \"block\"\n            }\n          | {\n              toggle: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"toggle\"\n              object?: \"block\"\n            }\n          | {\n              template: {\n                rich_text: Array<RichTextItemRequest>\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"template\"\n              object?: \"block\"\n            }\n          | {\n              callout: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                icon?:\n                  | { emoji: EmojiRequest; type?: \"emoji\" }\n                  | { external: { url: TextRequest }; type?: \"external\" }\n                  | {\n                      custom_emoji: {\n                        id: IdRequest\n                        name?: string\n                        url?: string\n                      }\n                      type?: \"custom_emoji\"\n                    }\n              }\n              type?: \"callout\"\n              object?: \"block\"\n            }\n          | {\n              synced_block: {\n                synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"synced_block\"\n              object?: \"block\"\n            }\n        >\n      }\n      type?: \"numbered_list_item\"\n      object?: \"block\"\n    }\n  | {\n      quote: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        children?: Array<\n          | {\n              embed: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"embed\"\n              object?: \"block\"\n            }\n          | {\n              bookmark: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"bookmark\"\n              object?: \"block\"\n            }\n          | {\n              image: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"image\"\n              object?: \"block\"\n            }\n          | {\n              video: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"video\"\n              object?: \"block\"\n            }\n          | {\n              pdf: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"pdf\"\n              object?: \"block\"\n            }\n          | {\n              file: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n                name?: StringRequest\n              }\n              type?: \"file\"\n              object?: \"block\"\n            }\n          | {\n              audio: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"audio\"\n              object?: \"block\"\n            }\n          | {\n              code: {\n                rich_text: Array<RichTextItemRequest>\n                language: LanguageRequest\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"code\"\n              object?: \"block\"\n            }\n          | {\n              equation: { expression: string }\n              type?: \"equation\"\n              object?: \"block\"\n            }\n          | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n          | { breadcrumb: EmptyObject; type?: \"breadcrumb\"; object?: \"block\" }\n          | {\n              table_of_contents: { color?: ApiColor }\n              type?: \"table_of_contents\"\n              object?: \"block\"\n            }\n          | {\n              link_to_page:\n                | { page_id: IdRequest; type?: \"page_id\" }\n                | { database_id: IdRequest; type?: \"database_id\" }\n                | { comment_id: IdRequest; type?: \"comment_id\" }\n              type?: \"link_to_page\"\n              object?: \"block\"\n            }\n          | {\n              table_row: { cells: Array<Array<RichTextItemRequest>> }\n              type?: \"table_row\"\n              object?: \"block\"\n            }\n          | {\n              heading_1: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_1\"\n              object?: \"block\"\n            }\n          | {\n              heading_2: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_2\"\n              object?: \"block\"\n            }\n          | {\n              heading_3: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_3\"\n              object?: \"block\"\n            }\n          | {\n              paragraph: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"paragraph\"\n              object?: \"block\"\n            }\n          | {\n              bulleted_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"bulleted_list_item\"\n              object?: \"block\"\n            }\n          | {\n              numbered_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"numbered_list_item\"\n              object?: \"block\"\n            }\n          | {\n              quote: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"quote\"\n              object?: \"block\"\n            }\n          | {\n              table: {\n                table_width: number\n                children: Array<{\n                  table_row: { cells: Array<Array<RichTextItemRequest>> }\n                  type?: \"table_row\"\n                  object?: \"block\"\n                }>\n                has_column_header?: boolean\n                has_row_header?: boolean\n              }\n              type?: \"table\"\n              object?: \"block\"\n            }\n          | {\n              to_do: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                checked?: boolean\n              }\n              type?: \"to_do\"\n              object?: \"block\"\n            }\n          | {\n              toggle: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"toggle\"\n              object?: \"block\"\n            }\n          | {\n              template: {\n                rich_text: Array<RichTextItemRequest>\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"template\"\n              object?: \"block\"\n            }\n          | {\n              callout: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                icon?:\n                  | { emoji: EmojiRequest; type?: \"emoji\" }\n                  | { external: { url: TextRequest }; type?: \"external\" }\n                  | {\n                      custom_emoji: {\n                        id: IdRequest\n                        name?: string\n                        url?: string\n                      }\n                      type?: \"custom_emoji\"\n                    }\n              }\n              type?: \"callout\"\n              object?: \"block\"\n            }\n          | {\n              synced_block: {\n                synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"synced_block\"\n              object?: \"block\"\n            }\n        >\n      }\n      type?: \"quote\"\n      object?: \"block\"\n    }\n  | {\n      to_do: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        children?: Array<\n          | {\n              embed: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"embed\"\n              object?: \"block\"\n            }\n          | {\n              bookmark: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"bookmark\"\n              object?: \"block\"\n            }\n          | {\n              image: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"image\"\n              object?: \"block\"\n            }\n          | {\n              video: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"video\"\n              object?: \"block\"\n            }\n          | {\n              pdf: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"pdf\"\n              object?: \"block\"\n            }\n          | {\n              file: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n                name?: StringRequest\n              }\n              type?: \"file\"\n              object?: \"block\"\n            }\n          | {\n              audio: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"audio\"\n              object?: \"block\"\n            }\n          | {\n              code: {\n                rich_text: Array<RichTextItemRequest>\n                language: LanguageRequest\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"code\"\n              object?: \"block\"\n            }\n          | {\n              equation: { expression: string }\n              type?: \"equation\"\n              object?: \"block\"\n            }\n          | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n          | { breadcrumb: EmptyObject; type?: \"breadcrumb\"; object?: \"block\" }\n          | {\n              table_of_contents: { color?: ApiColor }\n              type?: \"table_of_contents\"\n              object?: \"block\"\n            }\n          | {\n              link_to_page:\n                | { page_id: IdRequest; type?: \"page_id\" }\n                | { database_id: IdRequest; type?: \"database_id\" }\n                | { comment_id: IdRequest; type?: \"comment_id\" }\n              type?: \"link_to_page\"\n              object?: \"block\"\n            }\n          | {\n              table_row: { cells: Array<Array<RichTextItemRequest>> }\n              type?: \"table_row\"\n              object?: \"block\"\n            }\n          | {\n              heading_1: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_1\"\n              object?: \"block\"\n            }\n          | {\n              heading_2: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_2\"\n              object?: \"block\"\n            }\n          | {\n              heading_3: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_3\"\n              object?: \"block\"\n            }\n          | {\n              paragraph: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"paragraph\"\n              object?: \"block\"\n            }\n          | {\n              bulleted_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"bulleted_list_item\"\n              object?: \"block\"\n            }\n          | {\n              numbered_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"numbered_list_item\"\n              object?: \"block\"\n            }\n          | {\n              quote: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"quote\"\n              object?: \"block\"\n            }\n          | {\n              table: {\n                table_width: number\n                children: Array<{\n                  table_row: { cells: Array<Array<RichTextItemRequest>> }\n                  type?: \"table_row\"\n                  object?: \"block\"\n                }>\n                has_column_header?: boolean\n                has_row_header?: boolean\n              }\n              type?: \"table\"\n              object?: \"block\"\n            }\n          | {\n              to_do: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                checked?: boolean\n              }\n              type?: \"to_do\"\n              object?: \"block\"\n            }\n          | {\n              toggle: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"toggle\"\n              object?: \"block\"\n            }\n          | {\n              template: {\n                rich_text: Array<RichTextItemRequest>\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"template\"\n              object?: \"block\"\n            }\n          | {\n              callout: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                icon?:\n                  | { emoji: EmojiRequest; type?: \"emoji\" }\n                  | { external: { url: TextRequest }; type?: \"external\" }\n                  | {\n                      custom_emoji: {\n                        id: IdRequest\n                        name?: string\n                        url?: string\n                      }\n                      type?: \"custom_emoji\"\n                    }\n              }\n              type?: \"callout\"\n              object?: \"block\"\n            }\n          | {\n              synced_block: {\n                synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"synced_block\"\n              object?: \"block\"\n            }\n        >\n        checked?: boolean\n      }\n      type?: \"to_do\"\n      object?: \"block\"\n    }\n  | {\n      toggle: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        children?: Array<\n          | {\n              embed: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"embed\"\n              object?: \"block\"\n            }\n          | {\n              bookmark: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"bookmark\"\n              object?: \"block\"\n            }\n          | {\n              image: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"image\"\n              object?: \"block\"\n            }\n          | {\n              video: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"video\"\n              object?: \"block\"\n            }\n          | {\n              pdf: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"pdf\"\n              object?: \"block\"\n            }\n          | {\n              file: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n                name?: StringRequest\n              }\n              type?: \"file\"\n              object?: \"block\"\n            }\n          | {\n              audio: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"audio\"\n              object?: \"block\"\n            }\n          | {\n              code: {\n                rich_text: Array<RichTextItemRequest>\n                language: LanguageRequest\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"code\"\n              object?: \"block\"\n            }\n          | {\n              equation: { expression: string }\n              type?: \"equation\"\n              object?: \"block\"\n            }\n          | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n          | { breadcrumb: EmptyObject; type?: \"breadcrumb\"; object?: \"block\" }\n          | {\n              table_of_contents: { color?: ApiColor }\n              type?: \"table_of_contents\"\n              object?: \"block\"\n            }\n          | {\n              link_to_page:\n                | { page_id: IdRequest; type?: \"page_id\" }\n                | { database_id: IdRequest; type?: \"database_id\" }\n                | { comment_id: IdRequest; type?: \"comment_id\" }\n              type?: \"link_to_page\"\n              object?: \"block\"\n            }\n          | {\n              table_row: { cells: Array<Array<RichTextItemRequest>> }\n              type?: \"table_row\"\n              object?: \"block\"\n            }\n          | {\n              heading_1: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_1\"\n              object?: \"block\"\n            }\n          | {\n              heading_2: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_2\"\n              object?: \"block\"\n            }\n          | {\n              heading_3: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_3\"\n              object?: \"block\"\n            }\n          | {\n              paragraph: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"paragraph\"\n              object?: \"block\"\n            }\n          | {\n              bulleted_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"bulleted_list_item\"\n              object?: \"block\"\n            }\n          | {\n              numbered_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"numbered_list_item\"\n              object?: \"block\"\n            }\n          | {\n              quote: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"quote\"\n              object?: \"block\"\n            }\n          | {\n              table: {\n                table_width: number\n                children: Array<{\n                  table_row: { cells: Array<Array<RichTextItemRequest>> }\n                  type?: \"table_row\"\n                  object?: \"block\"\n                }>\n                has_column_header?: boolean\n                has_row_header?: boolean\n              }\n              type?: \"table\"\n              object?: \"block\"\n            }\n          | {\n              to_do: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                checked?: boolean\n              }\n              type?: \"to_do\"\n              object?: \"block\"\n            }\n          | {\n              toggle: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"toggle\"\n              object?: \"block\"\n            }\n          | {\n              template: {\n                rich_text: Array<RichTextItemRequest>\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"template\"\n              object?: \"block\"\n            }\n          | {\n              callout: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                icon?:\n                  | { emoji: EmojiRequest; type?: \"emoji\" }\n                  | { external: { url: TextRequest }; type?: \"external\" }\n                  | {\n                      custom_emoji: {\n                        id: IdRequest\n                        name?: string\n                        url?: string\n                      }\n                      type?: \"custom_emoji\"\n                    }\n              }\n              type?: \"callout\"\n              object?: \"block\"\n            }\n          | {\n              synced_block: {\n                synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"synced_block\"\n              object?: \"block\"\n            }\n        >\n      }\n      type?: \"toggle\"\n      object?: \"block\"\n    }\n  | {\n      template: {\n        rich_text: Array<RichTextItemRequest>\n        children?: Array<\n          | {\n              embed: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"embed\"\n              object?: \"block\"\n            }\n          | {\n              bookmark: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"bookmark\"\n              object?: \"block\"\n            }\n          | {\n              image: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"image\"\n              object?: \"block\"\n            }\n          | {\n              video: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"video\"\n              object?: \"block\"\n            }\n          | {\n              pdf: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"pdf\"\n              object?: \"block\"\n            }\n          | {\n              file: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n                name?: StringRequest\n              }\n              type?: \"file\"\n              object?: \"block\"\n            }\n          | {\n              audio: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"audio\"\n              object?: \"block\"\n            }\n          | {\n              code: {\n                rich_text: Array<RichTextItemRequest>\n                language: LanguageRequest\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"code\"\n              object?: \"block\"\n            }\n          | {\n              equation: { expression: string }\n              type?: \"equation\"\n              object?: \"block\"\n            }\n          | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n          | { breadcrumb: EmptyObject; type?: \"breadcrumb\"; object?: \"block\" }\n          | {\n              table_of_contents: { color?: ApiColor }\n              type?: \"table_of_contents\"\n              object?: \"block\"\n            }\n          | {\n              link_to_page:\n                | { page_id: IdRequest; type?: \"page_id\" }\n                | { database_id: IdRequest; type?: \"database_id\" }\n                | { comment_id: IdRequest; type?: \"comment_id\" }\n              type?: \"link_to_page\"\n              object?: \"block\"\n            }\n          | {\n              table_row: { cells: Array<Array<RichTextItemRequest>> }\n              type?: \"table_row\"\n              object?: \"block\"\n            }\n          | {\n              heading_1: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_1\"\n              object?: \"block\"\n            }\n          | {\n              heading_2: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_2\"\n              object?: \"block\"\n            }\n          | {\n              heading_3: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_3\"\n              object?: \"block\"\n            }\n          | {\n              paragraph: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"paragraph\"\n              object?: \"block\"\n            }\n          | {\n              bulleted_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"bulleted_list_item\"\n              object?: \"block\"\n            }\n          | {\n              numbered_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"numbered_list_item\"\n              object?: \"block\"\n            }\n          | {\n              quote: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"quote\"\n              object?: \"block\"\n            }\n          | {\n              table: {\n                table_width: number\n                children: Array<{\n                  table_row: { cells: Array<Array<RichTextItemRequest>> }\n                  type?: \"table_row\"\n                  object?: \"block\"\n                }>\n                has_column_header?: boolean\n                has_row_header?: boolean\n              }\n              type?: \"table\"\n              object?: \"block\"\n            }\n          | {\n              to_do: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                checked?: boolean\n              }\n              type?: \"to_do\"\n              object?: \"block\"\n            }\n          | {\n              toggle: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"toggle\"\n              object?: \"block\"\n            }\n          | {\n              template: {\n                rich_text: Array<RichTextItemRequest>\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"template\"\n              object?: \"block\"\n            }\n          | {\n              callout: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                icon?:\n                  | { emoji: EmojiRequest; type?: \"emoji\" }\n                  | { external: { url: TextRequest }; type?: \"external\" }\n                  | {\n                      custom_emoji: {\n                        id: IdRequest\n                        name?: string\n                        url?: string\n                      }\n                      type?: \"custom_emoji\"\n                    }\n              }\n              type?: \"callout\"\n              object?: \"block\"\n            }\n          | {\n              synced_block: {\n                synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"synced_block\"\n              object?: \"block\"\n            }\n        >\n      }\n      type?: \"template\"\n      object?: \"block\"\n    }\n  | {\n      callout: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        children?: Array<\n          | {\n              embed: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"embed\"\n              object?: \"block\"\n            }\n          | {\n              bookmark: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"bookmark\"\n              object?: \"block\"\n            }\n          | {\n              image: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"image\"\n              object?: \"block\"\n            }\n          | {\n              video: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"video\"\n              object?: \"block\"\n            }\n          | {\n              pdf: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"pdf\"\n              object?: \"block\"\n            }\n          | {\n              file: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n                name?: StringRequest\n              }\n              type?: \"file\"\n              object?: \"block\"\n            }\n          | {\n              audio: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"audio\"\n              object?: \"block\"\n            }\n          | {\n              code: {\n                rich_text: Array<RichTextItemRequest>\n                language: LanguageRequest\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"code\"\n              object?: \"block\"\n            }\n          | {\n              equation: { expression: string }\n              type?: \"equation\"\n              object?: \"block\"\n            }\n          | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n          | { breadcrumb: EmptyObject; type?: \"breadcrumb\"; object?: \"block\" }\n          | {\n              table_of_contents: { color?: ApiColor }\n              type?: \"table_of_contents\"\n              object?: \"block\"\n            }\n          | {\n              link_to_page:\n                | { page_id: IdRequest; type?: \"page_id\" }\n                | { database_id: IdRequest; type?: \"database_id\" }\n                | { comment_id: IdRequest; type?: \"comment_id\" }\n              type?: \"link_to_page\"\n              object?: \"block\"\n            }\n          | {\n              table_row: { cells: Array<Array<RichTextItemRequest>> }\n              type?: \"table_row\"\n              object?: \"block\"\n            }\n          | {\n              heading_1: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_1\"\n              object?: \"block\"\n            }\n          | {\n              heading_2: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_2\"\n              object?: \"block\"\n            }\n          | {\n              heading_3: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_3\"\n              object?: \"block\"\n            }\n          | {\n              paragraph: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"paragraph\"\n              object?: \"block\"\n            }\n          | {\n              bulleted_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"bulleted_list_item\"\n              object?: \"block\"\n            }\n          | {\n              numbered_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"numbered_list_item\"\n              object?: \"block\"\n            }\n          | {\n              quote: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"quote\"\n              object?: \"block\"\n            }\n          | {\n              table: {\n                table_width: number\n                children: Array<{\n                  table_row: { cells: Array<Array<RichTextItemRequest>> }\n                  type?: \"table_row\"\n                  object?: \"block\"\n                }>\n                has_column_header?: boolean\n                has_row_header?: boolean\n              }\n              type?: \"table\"\n              object?: \"block\"\n            }\n          | {\n              to_do: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                checked?: boolean\n              }\n              type?: \"to_do\"\n              object?: \"block\"\n            }\n          | {\n              toggle: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"toggle\"\n              object?: \"block\"\n            }\n          | {\n              template: {\n                rich_text: Array<RichTextItemRequest>\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"template\"\n              object?: \"block\"\n            }\n          | {\n              callout: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                icon?:\n                  | { emoji: EmojiRequest; type?: \"emoji\" }\n                  | { external: { url: TextRequest }; type?: \"external\" }\n                  | {\n                      custom_emoji: {\n                        id: IdRequest\n                        name?: string\n                        url?: string\n                      }\n                      type?: \"custom_emoji\"\n                    }\n              }\n              type?: \"callout\"\n              object?: \"block\"\n            }\n          | {\n              synced_block: {\n                synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"synced_block\"\n              object?: \"block\"\n            }\n        >\n        icon?:\n          | { emoji: EmojiRequest; type?: \"emoji\" }\n          | { external: { url: TextRequest }; type?: \"external\" }\n          | {\n              custom_emoji: { id: IdRequest; name?: string; url?: string }\n              type?: \"custom_emoji\"\n            }\n      }\n      type?: \"callout\"\n      object?: \"block\"\n    }\n  | {\n      synced_block: {\n        synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n        children?: Array<\n          | {\n              embed: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"embed\"\n              object?: \"block\"\n            }\n          | {\n              bookmark: { url: string; caption?: Array<RichTextItemRequest> }\n              type?: \"bookmark\"\n              object?: \"block\"\n            }\n          | {\n              image: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"image\"\n              object?: \"block\"\n            }\n          | {\n              video: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"video\"\n              object?: \"block\"\n            }\n          | {\n              pdf: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"pdf\"\n              object?: \"block\"\n            }\n          | {\n              file: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n                name?: StringRequest\n              }\n              type?: \"file\"\n              object?: \"block\"\n            }\n          | {\n              audio: {\n                external: { url: TextRequest }\n                type?: \"external\"\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"audio\"\n              object?: \"block\"\n            }\n          | {\n              code: {\n                rich_text: Array<RichTextItemRequest>\n                language: LanguageRequest\n                caption?: Array<RichTextItemRequest>\n              }\n              type?: \"code\"\n              object?: \"block\"\n            }\n          | {\n              equation: { expression: string }\n              type?: \"equation\"\n              object?: \"block\"\n            }\n          | { divider: EmptyObject; type?: \"divider\"; object?: \"block\" }\n          | { breadcrumb: EmptyObject; type?: \"breadcrumb\"; object?: \"block\" }\n          | {\n              table_of_contents: { color?: ApiColor }\n              type?: \"table_of_contents\"\n              object?: \"block\"\n            }\n          | {\n              link_to_page:\n                | { page_id: IdRequest; type?: \"page_id\" }\n                | { database_id: IdRequest; type?: \"database_id\" }\n                | { comment_id: IdRequest; type?: \"comment_id\" }\n              type?: \"link_to_page\"\n              object?: \"block\"\n            }\n          | {\n              table_row: { cells: Array<Array<RichTextItemRequest>> }\n              type?: \"table_row\"\n              object?: \"block\"\n            }\n          | {\n              heading_1: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_1\"\n              object?: \"block\"\n            }\n          | {\n              heading_2: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_2\"\n              object?: \"block\"\n            }\n          | {\n              heading_3: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                is_toggleable?: boolean\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"heading_3\"\n              object?: \"block\"\n            }\n          | {\n              paragraph: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"paragraph\"\n              object?: \"block\"\n            }\n          | {\n              bulleted_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"bulleted_list_item\"\n              object?: \"block\"\n            }\n          | {\n              numbered_list_item: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"numbered_list_item\"\n              object?: \"block\"\n            }\n          | {\n              quote: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"quote\"\n              object?: \"block\"\n            }\n          | {\n              table: {\n                table_width: number\n                children: Array<{\n                  table_row: { cells: Array<Array<RichTextItemRequest>> }\n                  type?: \"table_row\"\n                  object?: \"block\"\n                }>\n                has_column_header?: boolean\n                has_row_header?: boolean\n              }\n              type?: \"table\"\n              object?: \"block\"\n            }\n          | {\n              to_do: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                checked?: boolean\n              }\n              type?: \"to_do\"\n              object?: \"block\"\n            }\n          | {\n              toggle: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"toggle\"\n              object?: \"block\"\n            }\n          | {\n              template: {\n                rich_text: Array<RichTextItemRequest>\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"template\"\n              object?: \"block\"\n            }\n          | {\n              callout: {\n                rich_text: Array<RichTextItemRequest>\n                color?: ApiColor\n                children?: Array<BlockObjectRequestWithoutChildren>\n                icon?:\n                  | { emoji: EmojiRequest; type?: \"emoji\" }\n                  | { external: { url: TextRequest }; type?: \"external\" }\n                  | {\n                      custom_emoji: {\n                        id: IdRequest\n                        name?: string\n                        url?: string\n                      }\n                      type?: \"custom_emoji\"\n                    }\n              }\n              type?: \"callout\"\n              object?: \"block\"\n            }\n          | {\n              synced_block: {\n                synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n                children?: Array<BlockObjectRequestWithoutChildren>\n              }\n              type?: \"synced_block\"\n              object?: \"block\"\n            }\n        >\n      }\n      type?: \"synced_block\"\n      object?: \"block\"\n    }\n\ntype ExistencePropertyFilter = { is_empty: true } | { is_not_empty: true }\n\ntype TextPropertyFilter =\n  | { equals: string }\n  | { does_not_equal: string }\n  | { contains: string }\n  | { does_not_contain: string }\n  | { starts_with: string }\n  | { ends_with: string }\n  | ExistencePropertyFilter\n\ntype NumberPropertyFilter =\n  | { equals: number }\n  | { does_not_equal: number }\n  | { greater_than: number }\n  | { less_than: number }\n  | { greater_than_or_equal_to: number }\n  | { less_than_or_equal_to: number }\n  | ExistencePropertyFilter\n\ntype CheckboxPropertyFilter = { equals: boolean } | { does_not_equal: boolean }\n\ntype SelectPropertyFilter =\n  | { equals: string }\n  | { does_not_equal: string }\n  | ExistencePropertyFilter\n\ntype MultiSelectPropertyFilter =\n  | { contains: string }\n  | { does_not_contain: string }\n  | ExistencePropertyFilter\n\ntype StatusPropertyFilter =\n  | { equals: string }\n  | { does_not_equal: string }\n  | ExistencePropertyFilter\n\ntype DatePropertyFilter =\n  | { equals: string }\n  | { before: string }\n  | { after: string }\n  | { on_or_before: string }\n  | { on_or_after: string }\n  | { this_week: EmptyObject }\n  | { past_week: EmptyObject }\n  | { past_month: EmptyObject }\n  | { past_year: EmptyObject }\n  | { next_week: EmptyObject }\n  | { next_month: EmptyObject }\n  | { next_year: EmptyObject }\n  | ExistencePropertyFilter\n\ntype PeoplePropertyFilter =\n  | { contains: IdRequest }\n  | { does_not_contain: IdRequest }\n  | ExistencePropertyFilter\n\ntype RelationPropertyFilter =\n  | { contains: IdRequest }\n  | { does_not_contain: IdRequest }\n  | ExistencePropertyFilter\n\ntype FormulaPropertyFilter =\n  | { string: TextPropertyFilter }\n  | { checkbox: CheckboxPropertyFilter }\n  | { number: NumberPropertyFilter }\n  | { date: DatePropertyFilter }\n\ntype RollupSubfilterPropertyFilter =\n  | { rich_text: TextPropertyFilter }\n  | { number: NumberPropertyFilter }\n  | { checkbox: CheckboxPropertyFilter }\n  | { select: SelectPropertyFilter }\n  | { multi_select: MultiSelectPropertyFilter }\n  | { relation: RelationPropertyFilter }\n  | { date: DatePropertyFilter }\n  | { people: PeoplePropertyFilter }\n  | { files: ExistencePropertyFilter }\n  | { status: StatusPropertyFilter }\n\ntype RollupPropertyFilter =\n  | { any: RollupSubfilterPropertyFilter }\n  | { none: RollupSubfilterPropertyFilter }\n  | { every: RollupSubfilterPropertyFilter }\n  | { date: DatePropertyFilter }\n  | { number: NumberPropertyFilter }\n\ntype PropertyFilter =\n  | { title: TextPropertyFilter; property: string; type?: \"title\" }\n  | { rich_text: TextPropertyFilter; property: string; type?: \"rich_text\" }\n  | { number: NumberPropertyFilter; property: string; type?: \"number\" }\n  | { checkbox: CheckboxPropertyFilter; property: string; type?: \"checkbox\" }\n  | { select: SelectPropertyFilter; property: string; type?: \"select\" }\n  | {\n      multi_select: MultiSelectPropertyFilter\n      property: string\n      type?: \"multi_select\"\n    }\n  | { status: StatusPropertyFilter; property: string; type?: \"status\" }\n  | { date: DatePropertyFilter; property: string; type?: \"date\" }\n  | { people: PeoplePropertyFilter; property: string; type?: \"people\" }\n  | { files: ExistencePropertyFilter; property: string; type?: \"files\" }\n  | { url: TextPropertyFilter; property: string; type?: \"url\" }\n  | { email: TextPropertyFilter; property: string; type?: \"email\" }\n  | {\n      phone_number: TextPropertyFilter\n      property: string\n      type?: \"phone_number\"\n    }\n  | { relation: RelationPropertyFilter; property: string; type?: \"relation\" }\n  | { created_by: PeoplePropertyFilter; property: string; type?: \"created_by\" }\n  | {\n      created_time: DatePropertyFilter\n      property: string\n      type?: \"created_time\"\n    }\n  | {\n      last_edited_by: PeoplePropertyFilter\n      property: string\n      type?: \"last_edited_by\"\n    }\n  | {\n      last_edited_time: DatePropertyFilter\n      property: string\n      type?: \"last_edited_time\"\n    }\n  | { formula: FormulaPropertyFilter; property: string; type?: \"formula\" }\n  | { unique_id: NumberPropertyFilter; property: string; type?: \"unique_id\" }\n  | { rollup: RollupPropertyFilter; property: string; type?: \"rollup\" }\n\ntype TimestampCreatedTimeFilter = {\n  created_time: DatePropertyFilter\n  timestamp: \"created_time\"\n  type?: \"created_time\"\n}\n\ntype TimestampLastEditedTimeFilter = {\n  last_edited_time: DatePropertyFilter\n  timestamp: \"last_edited_time\"\n  type?: \"last_edited_time\"\n}\nexport type GetSelfParameters = Record<string, never>\n\nexport type GetSelfResponse = UserObjectResponse\n\nexport const getSelf = {\n  method: \"get\",\n  pathParams: [],\n  queryParams: [],\n  bodyParams: [],\n  path: (): string => `users/me`,\n} as const\n\ntype GetUserPathParameters = {\n  user_id: IdRequest\n}\n\nexport type GetUserParameters = GetUserPathParameters\n\nexport type GetUserResponse = UserObjectResponse\n\nexport const getUser = {\n  method: \"get\",\n  pathParams: [\"user_id\"],\n  queryParams: [],\n  bodyParams: [],\n  path: (p: GetUserPathParameters): string => `users/${p.user_id}`,\n} as const\n\ntype ListUsersQueryParameters = {\n  start_cursor?: string\n  page_size?: number\n}\n\nexport type ListUsersParameters = ListUsersQueryParameters\n\nexport type ListUsersResponse = {\n  type: \"user\"\n  user: EmptyObject\n  object: \"list\"\n  next_cursor: string | null\n  has_more: boolean\n  results: Array<UserObjectResponse>\n}\n\nexport const listUsers = {\n  method: \"get\",\n  pathParams: [],\n  queryParams: [\"start_cursor\", \"page_size\"],\n  bodyParams: [],\n  path: (): string => `users`,\n} as const\n\ntype CreatePageBodyParameters = {\n  parent:\n    | { page_id: IdRequest; type?: \"page_id\" }\n    | { database_id: IdRequest; type?: \"database_id\" }\n  properties:\n    | Record<\n        string,\n        | { title: Array<RichTextItemRequest>; type?: \"title\" }\n        | { rich_text: Array<RichTextItemRequest>; type?: \"rich_text\" }\n        | { number: number | null; type?: \"number\" }\n        | { url: TextRequest | null; type?: \"url\" }\n        | {\n            select:\n              | {\n                  id: StringRequest\n                  name?: StringRequest\n                  color?: SelectColor\n                  description?: StringRequest | null\n                }\n              | null\n              | {\n                  name: StringRequest\n                  id?: StringRequest\n                  color?: SelectColor\n                  description?: StringRequest | null\n                }\n              | null\n            type?: \"select\"\n          }\n        | {\n            multi_select: Array<\n              | {\n                  id: StringRequest\n                  name?: StringRequest\n                  color?: SelectColor\n                  description?: StringRequest | null\n                }\n              | {\n                  name: StringRequest\n                  id?: StringRequest\n                  color?: SelectColor\n                  description?: StringRequest | null\n                }\n            >\n            type?: \"multi_select\"\n          }\n        | {\n            people: Array<\n              | { id: IdRequest }\n              | {\n                  person: { email?: string }\n                  id: IdRequest\n                  type?: \"person\"\n                  name?: string | null\n                  avatar_url?: string | null\n                  object?: \"user\"\n                }\n              | {\n                  bot:\n                    | EmptyObject\n                    | {\n                        owner:\n                          | {\n                              type: \"user\"\n                              user:\n                                | {\n                                    type: \"person\"\n                                    person: { email: string }\n                                    name: string | null\n                                    avatar_url: string | null\n                                    id: IdRequest\n                                    object: \"user\"\n                                  }\n                                | PartialUserObjectResponse\n                            }\n                          | { type: \"workspace\"; workspace: true }\n                        workspace_name: string | null\n                      }\n                  id: IdRequest\n                  type?: \"bot\"\n                  name?: string | null\n                  avatar_url?: string | null\n                  object?: \"user\"\n                }\n            >\n            type?: \"people\"\n          }\n        | { email: StringRequest | null; type?: \"email\" }\n        | { phone_number: StringRequest | null; type?: \"phone_number\" }\n        | { date: DateRequest | null; type?: \"date\" }\n        | { checkbox: boolean; type?: \"checkbox\" }\n        | { relation: Array<{ id: IdRequest }>; type?: \"relation\" }\n        | {\n            files: Array<\n              | {\n                  file: { url: string; expiry_time?: string }\n                  name: StringRequest\n                  type?: \"file\"\n                }\n              | {\n                  external: { url: TextRequest }\n                  name: StringRequest\n                  type?: \"external\"\n                }\n            >\n            type?: \"files\"\n          }\n        | {\n            status:\n              | {\n                  id: StringRequest\n                  name?: StringRequest\n                  color?: SelectColor\n                  description?: StringRequest | null\n                }\n              | null\n              | {\n                  name: StringRequest\n                  id?: StringRequest\n                  color?: SelectColor\n                  description?: StringRequest | null\n                }\n              | null\n            type?: \"status\"\n          }\n      >\n    | Record<\n        string,\n        | Array<RichTextItemRequest>\n        | Array<RichTextItemRequest>\n        | number\n        | null\n        | TextRequest\n        | null\n        | {\n            id: StringRequest\n            name?: StringRequest\n            color?: SelectColor\n            description?: StringRequest | null\n          }\n        | null\n        | {\n            name: StringRequest\n            id?: StringRequest\n            color?: SelectColor\n            description?: StringRequest | null\n          }\n        | null\n        | Array<\n            | {\n                id: StringRequest\n                name?: StringRequest\n                color?: SelectColor\n                description?: StringRequest | null\n              }\n            | {\n                name: StringRequest\n                id?: StringRequest\n                color?: SelectColor\n                description?: StringRequest | null\n              }\n          >\n        | Array<\n            | { id: IdRequest }\n            | {\n                person: { email?: string }\n                id: IdRequest\n                type?: \"person\"\n                name?: string | null\n                avatar_url?: string | null\n                object?: \"user\"\n              }\n            | {\n                bot:\n                  | EmptyObject\n                  | {\n                      owner:\n                        | {\n                            type: \"user\"\n                            user:\n                              | {\n                                  type: \"person\"\n                                  person: { email: string }\n                                  name: string | null\n                                  avatar_url: string | null\n                                  id: IdRequest\n                                  object: \"user\"\n                                }\n                              | PartialUserObjectResponse\n                          }\n                        | { type: \"workspace\"; workspace: true }\n                      workspace_name: string | null\n                    }\n                id: IdRequest\n                type?: \"bot\"\n                name?: string | null\n                avatar_url?: string | null\n                object?: \"user\"\n              }\n          >\n        | StringRequest\n        | null\n        | StringRequest\n        | null\n        | DateRequest\n        | null\n        | boolean\n        | Array<{ id: IdRequest }>\n        | Array<\n            | {\n                file: { url: string; expiry_time?: string }\n                name: StringRequest\n                type?: \"file\"\n              }\n            | {\n                external: { url: TextRequest }\n                name: StringRequest\n                type?: \"external\"\n              }\n          >\n        | {\n            id: StringRequest\n            name?: StringRequest\n            color?: SelectColor\n            description?: StringRequest | null\n          }\n        | null\n        | {\n            name: StringRequest\n            id?: StringRequest\n            color?: SelectColor\n            description?: StringRequest | null\n          }\n        | null\n      >\n  icon?:\n    | { emoji: EmojiRequest; type?: \"emoji\" }\n    | null\n    | { external: { url: TextRequest }; type?: \"external\" }\n    | null\n    | {\n        custom_emoji: { id: IdRequest; name?: string; url?: string }\n        type?: \"custom_emoji\"\n      }\n    | null\n  cover?: { external: { url: TextRequest }; type?: \"external\" } | null\n  content?: Array<BlockObjectRequest>\n  children?: Array<BlockObjectRequest>\n}\n\nexport type CreatePageParameters = CreatePageBodyParameters\n\nexport type CreatePageResponse = PageObjectResponse | PartialPageObjectResponse\n\nexport const createPage = {\n  method: \"post\",\n  pathParams: [],\n  queryParams: [],\n  bodyParams: [\"parent\", \"properties\", \"icon\", \"cover\", \"content\", \"children\"],\n  path: (): string => `pages`,\n} as const\n\ntype GetPagePathParameters = {\n  page_id: IdRequest\n}\n\ntype GetPageQueryParameters = {\n  filter_properties?: Array<string>\n}\n\nexport type GetPageParameters = GetPagePathParameters & GetPageQueryParameters\n\nexport type GetPageResponse = PageObjectResponse | PartialPageObjectResponse\n\nexport const getPage = {\n  method: \"get\",\n  pathParams: [\"page_id\"],\n  queryParams: [\"filter_properties\"],\n  bodyParams: [],\n  path: (p: GetPagePathParameters): string => `pages/${p.page_id}`,\n} as const\n\ntype UpdatePagePathParameters = {\n  page_id: IdRequest\n}\n\ntype UpdatePageBodyParameters = {\n  properties?:\n    | Record<\n        string,\n        | { title: Array<RichTextItemRequest>; type?: \"title\" }\n        | { rich_text: Array<RichTextItemRequest>; type?: \"rich_text\" }\n        | { number: number | null; type?: \"number\" }\n        | { url: TextRequest | null; type?: \"url\" }\n        | {\n            select:\n              | {\n                  id: StringRequest\n                  name?: StringRequest\n                  color?: SelectColor\n                  description?: StringRequest | null\n                }\n              | null\n              | {\n                  name: StringRequest\n                  id?: StringRequest\n                  color?: SelectColor\n                  description?: StringRequest | null\n                }\n              | null\n            type?: \"select\"\n          }\n        | {\n            multi_select: Array<\n              | {\n                  id: StringRequest\n                  name?: StringRequest\n                  color?: SelectColor\n                  description?: StringRequest | null\n                }\n              | {\n                  name: StringRequest\n                  id?: StringRequest\n                  color?: SelectColor\n                  description?: StringRequest | null\n                }\n            >\n            type?: \"multi_select\"\n          }\n        | {\n            people: Array<\n              | { id: IdRequest }\n              | {\n                  person: { email?: string }\n                  id: IdRequest\n                  type?: \"person\"\n                  name?: string | null\n                  avatar_url?: string | null\n                  object?: \"user\"\n                }\n              | {\n                  bot:\n                    | EmptyObject\n                    | {\n                        owner:\n                          | {\n                              type: \"user\"\n                              user:\n                                | {\n                                    type: \"person\"\n                                    person: { email: string }\n                                    name: string | null\n                                    avatar_url: string | null\n                                    id: IdRequest\n                                    object: \"user\"\n                                  }\n                                | PartialUserObjectResponse\n                            }\n                          | { type: \"workspace\"; workspace: true }\n                        workspace_name: string | null\n                      }\n                  id: IdRequest\n                  type?: \"bot\"\n                  name?: string | null\n                  avatar_url?: string | null\n                  object?: \"user\"\n                }\n            >\n            type?: \"people\"\n          }\n        | { email: StringRequest | null; type?: \"email\" }\n        | { phone_number: StringRequest | null; type?: \"phone_number\" }\n        | { date: DateRequest | null; type?: \"date\" }\n        | { checkbox: boolean; type?: \"checkbox\" }\n        | { relation: Array<{ id: IdRequest }>; type?: \"relation\" }\n        | {\n            files: Array<\n              | {\n                  file: { url: string; expiry_time?: string }\n                  name: StringRequest\n                  type?: \"file\"\n                }\n              | {\n                  external: { url: TextRequest }\n                  name: StringRequest\n                  type?: \"external\"\n                }\n            >\n            type?: \"files\"\n          }\n        | {\n            status:\n              | {\n                  id: StringRequest\n                  name?: StringRequest\n                  color?: SelectColor\n                  description?: StringRequest | null\n                }\n              | null\n              | {\n                  name: StringRequest\n                  id?: StringRequest\n                  color?: SelectColor\n                  description?: StringRequest | null\n                }\n              | null\n            type?: \"status\"\n          }\n      >\n    | Record<\n        string,\n        | Array<RichTextItemRequest>\n        | Array<RichTextItemRequest>\n        | number\n        | null\n        | TextRequest\n        | null\n        | {\n            id: StringRequest\n            name?: StringRequest\n            color?: SelectColor\n            description?: StringRequest | null\n          }\n        | null\n        | {\n            name: StringRequest\n            id?: StringRequest\n            color?: SelectColor\n            description?: StringRequest | null\n          }\n        | null\n        | Array<\n            | {\n                id: StringRequest\n                name?: StringRequest\n                color?: SelectColor\n                description?: StringRequest | null\n              }\n            | {\n                name: StringRequest\n                id?: StringRequest\n                color?: SelectColor\n                description?: StringRequest | null\n              }\n          >\n        | Array<\n            | { id: IdRequest }\n            | {\n                person: { email?: string }\n                id: IdRequest\n                type?: \"person\"\n                name?: string | null\n                avatar_url?: string | null\n                object?: \"user\"\n              }\n            | {\n                bot:\n                  | EmptyObject\n                  | {\n                      owner:\n                        | {\n                            type: \"user\"\n                            user:\n                              | {\n                                  type: \"person\"\n                                  person: { email: string }\n                                  name: string | null\n                                  avatar_url: string | null\n                                  id: IdRequest\n                                  object: \"user\"\n                                }\n                              | PartialUserObjectResponse\n                          }\n                        | { type: \"workspace\"; workspace: true }\n                      workspace_name: string | null\n                    }\n                id: IdRequest\n                type?: \"bot\"\n                name?: string | null\n                avatar_url?: string | null\n                object?: \"user\"\n              }\n          >\n        | StringRequest\n        | null\n        | StringRequest\n        | null\n        | DateRequest\n        | null\n        | boolean\n        | Array<{ id: IdRequest }>\n        | Array<\n            | {\n                file: { url: string; expiry_time?: string }\n                name: StringRequest\n                type?: \"file\"\n              }\n            | {\n                external: { url: TextRequest }\n                name: StringRequest\n                type?: \"external\"\n              }\n          >\n        | {\n            id: StringRequest\n            name?: StringRequest\n            color?: SelectColor\n            description?: StringRequest | null\n          }\n        | null\n        | {\n            name: StringRequest\n            id?: StringRequest\n            color?: SelectColor\n            description?: StringRequest | null\n          }\n        | null\n      >\n  icon?:\n    | { emoji: EmojiRequest; type?: \"emoji\" }\n    | null\n    | { external: { url: TextRequest }; type?: \"external\" }\n    | null\n    | {\n        custom_emoji: { id: IdRequest; name?: string; url?: string }\n        type?: \"custom_emoji\"\n      }\n    | null\n  cover?: { external: { url: TextRequest }; type?: \"external\" } | null\n  archived?: boolean\n  in_trash?: boolean\n}\n\nexport type UpdatePageParameters = UpdatePagePathParameters &\n  UpdatePageBodyParameters\n\nexport type UpdatePageResponse = PageObjectResponse | PartialPageObjectResponse\n\nexport const updatePage = {\n  method: \"patch\",\n  pathParams: [\"page_id\"],\n  queryParams: [],\n  bodyParams: [\"properties\", \"icon\", \"cover\", \"archived\", \"in_trash\"],\n  path: (p: UpdatePagePathParameters): string => `pages/${p.page_id}`,\n} as const\n\ntype GetPagePropertyPathParameters = {\n  page_id: IdRequest\n  property_id: string\n}\n\ntype GetPagePropertyQueryParameters = {\n  start_cursor?: string\n  page_size?: number\n}\n\nexport type GetPagePropertyParameters = GetPagePropertyPathParameters &\n  GetPagePropertyQueryParameters\n\nexport type GetPagePropertyResponse =\n  | PropertyItemObjectResponse\n  | PropertyItemListResponse\n\nexport const getPageProperty = {\n  method: \"get\",\n  pathParams: [\"page_id\", \"property_id\"],\n  queryParams: [\"start_cursor\", \"page_size\"],\n  bodyParams: [],\n  path: (p: GetPagePropertyPathParameters): string =>\n    `pages/${p.page_id}/properties/${p.property_id}`,\n} as const\n\ntype GetBlockPathParameters = {\n  block_id: IdRequest\n}\n\nexport type GetBlockParameters = GetBlockPathParameters\n\nexport type GetBlockResponse = PartialBlockObjectResponse | BlockObjectResponse\n\nexport const getBlock = {\n  method: \"get\",\n  pathParams: [\"block_id\"],\n  queryParams: [],\n  bodyParams: [],\n  path: (p: GetBlockPathParameters): string => `blocks/${p.block_id}`,\n} as const\n\ntype UpdateBlockPathParameters = {\n  block_id: IdRequest\n}\n\ntype UpdateBlockBodyParameters =\n  | {\n      embed: { url?: string; caption?: Array<RichTextItemRequest> }\n      type?: \"embed\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      bookmark: { url?: string; caption?: Array<RichTextItemRequest> }\n      type?: \"bookmark\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      image: {\n        caption?: Array<RichTextItemRequest>\n        external?: { url: TextRequest }\n      }\n      type?: \"image\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      video: {\n        caption?: Array<RichTextItemRequest>\n        external?: { url: TextRequest }\n      }\n      type?: \"video\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      pdf: {\n        caption?: Array<RichTextItemRequest>\n        external?: { url: TextRequest }\n      }\n      type?: \"pdf\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      file: {\n        caption?: Array<RichTextItemRequest>\n        external?: { url: TextRequest }\n        name?: StringRequest\n      }\n      type?: \"file\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      audio: {\n        caption?: Array<RichTextItemRequest>\n        external?: { url: TextRequest }\n      }\n      type?: \"audio\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      code: {\n        rich_text?: Array<RichTextItemRequest>\n        language?: LanguageRequest\n        caption?: Array<RichTextItemRequest>\n      }\n      type?: \"code\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      equation: { expression: string }\n      type?: \"equation\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      divider: EmptyObject\n      type?: \"divider\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      breadcrumb: EmptyObject\n      type?: \"breadcrumb\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      table_of_contents: { color?: ApiColor }\n      type?: \"table_of_contents\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      link_to_page:\n        | { page_id: IdRequest; type?: \"page_id\" }\n        | { database_id: IdRequest; type?: \"database_id\" }\n        | { comment_id: IdRequest; type?: \"comment_id\" }\n      type?: \"link_to_page\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      table_row: { cells: Array<Array<RichTextItemRequest>> }\n      type?: \"table_row\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      heading_1: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        is_toggleable?: boolean\n      }\n      type?: \"heading_1\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      heading_2: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        is_toggleable?: boolean\n      }\n      type?: \"heading_2\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      heading_3: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n        is_toggleable?: boolean\n      }\n      type?: \"heading_3\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      paragraph: { rich_text: Array<RichTextItemRequest>; color?: ApiColor }\n      type?: \"paragraph\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      bulleted_list_item: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n      }\n      type?: \"bulleted_list_item\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      numbered_list_item: {\n        rich_text: Array<RichTextItemRequest>\n        color?: ApiColor\n      }\n      type?: \"numbered_list_item\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      quote: { rich_text: Array<RichTextItemRequest>; color?: ApiColor }\n      type?: \"quote\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      to_do: {\n        rich_text?: Array<RichTextItemRequest>\n        checked?: boolean\n        color?: ApiColor\n      }\n      type?: \"to_do\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      toggle: { rich_text: Array<RichTextItemRequest>; color?: ApiColor }\n      type?: \"toggle\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      template: { rich_text: Array<RichTextItemRequest> }\n      type?: \"template\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      callout: {\n        rich_text?: Array<RichTextItemRequest>\n        icon?:\n          | { emoji: EmojiRequest; type?: \"emoji\" }\n          | { external: { url: TextRequest }; type?: \"external\" }\n          | {\n              custom_emoji: { id: IdRequest; name?: string; url?: string }\n              type?: \"custom_emoji\"\n            }\n        color?: ApiColor\n      }\n      type?: \"callout\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      synced_block: {\n        synced_from: { block_id: IdRequest; type?: \"block_id\" } | null\n      }\n      type?: \"synced_block\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | {\n      table: { has_column_header?: boolean; has_row_header?: boolean }\n      type?: \"table\"\n      archived?: boolean\n      in_trash?: boolean\n    }\n  | { archived?: boolean; in_trash?: boolean }\n\nexport type UpdateBlockParameters = UpdateBlockPathParameters &\n  UpdateBlockBodyParameters\n\nexport type UpdateBlockResponse =\n  | PartialBlockObjectResponse\n  | BlockObjectResponse\n\nexport const updateBlock = {\n  method: \"patch\",\n  pathParams: [\"block_id\"],\n  queryParams: [],\n  bodyParams: [\n    \"embed\",\n    \"type\",\n    \"archived\",\n    \"in_trash\",\n    \"bookmark\",\n    \"image\",\n    \"video\",\n    \"pdf\",\n    \"file\",\n    \"audio\",\n    \"code\",\n    \"equation\",\n    \"divider\",\n    \"breadcrumb\",\n    \"table_of_contents\",\n    \"link_to_page\",\n    \"table_row\",\n    \"heading_1\",\n    \"heading_2\",\n    \"heading_3\",\n    \"paragraph\",\n    \"bulleted_list_item\",\n    \"numbered_list_item\",\n    \"quote\",\n    \"to_do\",\n    \"toggle\",\n    \"template\",\n    \"callout\",\n    \"synced_block\",\n    \"table\",\n  ],\n  path: (p: UpdateBlockPathParameters): string => `blocks/${p.block_id}`,\n} as const\n\ntype DeleteBlockPathParameters = {\n  block_id: IdRequest\n}\n\nexport type DeleteBlockParameters = DeleteBlockPathParameters\n\nexport type DeleteBlockResponse =\n  | PartialBlockObjectResponse\n  | BlockObjectResponse\n\nexport const deleteBlock = {\n  method: \"delete\",\n  pathParams: [\"block_id\"],\n  queryParams: [],\n  bodyParams: [],\n  path: (p: DeleteBlockPathParameters): string => `blocks/${p.block_id}`,\n} as const\n\ntype ListBlockChildrenPathParameters = {\n  block_id: IdRequest\n}\n\ntype ListBlockChildrenQueryParameters = {\n  start_cursor?: string\n  page_size?: number\n}\n\nexport type ListBlockChildrenParameters = ListBlockChildrenPathParameters &\n  ListBlockChildrenQueryParameters\n\nexport type ListBlockChildrenResponse = {\n  type: \"block\"\n  block: EmptyObject\n  object: \"list\"\n  next_cursor: string | null\n  has_more: boolean\n  results: Array<PartialBlockObjectResponse | BlockObjectResponse>\n}\n\nexport const listBlockChildren = {\n  method: \"get\",\n  pathParams: [\"block_id\"],\n  queryParams: [\"start_cursor\", \"page_size\"],\n  bodyParams: [],\n  path: (p: ListBlockChildrenPathParameters): string =>\n    `blocks/${p.block_id}/children`,\n} as const\n\ntype AppendBlockChildrenPathParameters = {\n  block_id: IdRequest\n}\n\ntype AppendBlockChildrenBodyParameters = {\n  children: Array<BlockObjectRequest>\n  after?: IdRequest\n}\n\nexport type AppendBlockChildrenParameters = AppendBlockChildrenPathParameters &\n  AppendBlockChildrenBodyParameters\n\nexport type AppendBlockChildrenResponse = {\n  type: \"block\"\n  block: EmptyObject\n  object: \"list\"\n  next_cursor: string | null\n  has_more: boolean\n  results: Array<PartialBlockObjectResponse | BlockObjectResponse>\n}\n\nexport const appendBlockChildren = {\n  method: \"patch\",\n  pathParams: [\"block_id\"],\n  queryParams: [],\n  bodyParams: [\"children\", \"after\"],\n  path: (p: AppendBlockChildrenPathParameters): string =>\n    `blocks/${p.block_id}/children`,\n} as const\n\ntype GetDatabasePathParameters = {\n  database_id: IdRequest\n}\n\nexport type GetDatabaseParameters = GetDatabasePathParameters\n\nexport type GetDatabaseResponse =\n  | PartialDatabaseObjectResponse\n  | DatabaseObjectResponse\n\nexport const getDatabase = {\n  method: \"get\",\n  pathParams: [\"database_id\"],\n  queryParams: [],\n  bodyParams: [],\n  path: (p: GetDatabasePathParameters): string => `databases/${p.database_id}`,\n} as const\n\ntype UpdateDatabasePathParameters = {\n  database_id: IdRequest\n}\n\ntype UpdateDatabaseBodyParameters = {\n  title?: Array<RichTextItemRequest>\n  description?: Array<RichTextItemRequest>\n  icon?:\n    | { emoji: EmojiRequest; type?: \"emoji\" }\n    | null\n    | { external: { url: TextRequest }; type?: \"external\" }\n    | null\n    | {\n        custom_emoji: { id: IdRequest; name?: string; url?: string }\n        type?: \"custom_emoji\"\n      }\n    | null\n  cover?: { external: { url: TextRequest }; type?: \"external\" } | null\n  properties?: Record<\n    string,\n    | {\n        number: { format?: NumberFormat }\n        type?: \"number\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        formula: { expression?: string }\n        type?: \"formula\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        select: {\n          options?: Array<\n            | {\n                id: StringRequest\n                name?: StringRequest\n                color?: SelectColor\n                description?: StringRequest | null\n              }\n            | {\n                name: StringRequest\n                id?: StringRequest\n                color?: SelectColor\n                description?: StringRequest | null\n              }\n          >\n        }\n        type?: \"select\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        multi_select: {\n          options?: Array<\n            | {\n                id: StringRequest\n                name?: StringRequest\n                color?: SelectColor\n                description?: StringRequest | null\n              }\n            | {\n                name: StringRequest\n                id?: StringRequest\n                color?: SelectColor\n                description?: StringRequest | null\n              }\n          >\n        }\n        type?: \"multi_select\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        relation:\n          | {\n              single_property: EmptyObject\n              database_id: IdRequest\n              type?: \"single_property\"\n            }\n          | {\n              dual_property: Record<string, never>\n              database_id: IdRequest\n              type?: \"dual_property\"\n            }\n        type?: \"relation\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        rollup:\n          | {\n              rollup_property_name: string\n              relation_property_name: string\n              function: RollupFunction\n              rollup_property_id?: string\n              relation_property_id?: string\n            }\n          | {\n              rollup_property_name: string\n              relation_property_id: string\n              function: RollupFunction\n              relation_property_name?: string\n              rollup_property_id?: string\n            }\n          | {\n              relation_property_name: string\n              rollup_property_id: string\n              function: RollupFunction\n              rollup_property_name?: string\n              relation_property_id?: string\n            }\n          | {\n              rollup_property_id: string\n              relation_property_id: string\n              function: RollupFunction\n              rollup_property_name?: string\n              relation_property_name?: string\n            }\n        type?: \"rollup\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        unique_id: { prefix?: string | null }\n        type?: \"unique_id\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        title: EmptyObject\n        type?: \"title\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        rich_text: EmptyObject\n        type?: \"rich_text\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        url: EmptyObject\n        type?: \"url\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        people: EmptyObject\n        type?: \"people\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        files: EmptyObject\n        type?: \"files\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        email: EmptyObject\n        type?: \"email\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        phone_number: EmptyObject\n        type?: \"phone_number\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        date: EmptyObject\n        type?: \"date\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        checkbox: EmptyObject\n        type?: \"checkbox\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        created_by: EmptyObject\n        type?: \"created_by\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        created_time: EmptyObject\n        type?: \"created_time\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        last_edited_by: EmptyObject\n        type?: \"last_edited_by\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | {\n        last_edited_time: EmptyObject\n        type?: \"last_edited_time\"\n        name?: string\n        description?: PropertyDescriptionRequest | null\n      }\n    | null\n    | { name: string }\n    | null\n  >\n  is_inline?: boolean\n  archived?: boolean\n  in_trash?: boolean\n}\n\nexport type UpdateDatabaseParameters = UpdateDatabasePathParameters &\n  UpdateDatabaseBodyParameters\n\nexport type UpdateDatabaseResponse =\n  | PartialDatabaseObjectResponse\n  | DatabaseObjectResponse\n\nexport const updateDatabase = {\n  method: \"patch\",\n  pathParams: [\"database_id\"],\n  queryParams: [],\n  bodyParams: [\n    \"title\",\n    \"description\",\n    \"icon\",\n    \"cover\",\n    \"properties\",\n    \"is_inline\",\n    \"archived\",\n    \"in_trash\",\n  ],\n  path: (p: UpdateDatabasePathParameters): string =>\n    `databases/${p.database_id}`,\n} as const\n\ntype QueryDatabasePathParameters = {\n  database_id: IdRequest\n}\n\ntype QueryDatabaseQueryParameters = {\n  filter_properties?: Array<string>\n}\n\ntype QueryDatabaseBodyParameters = {\n  sorts?: Array<\n    | { property: string; direction: \"ascending\" | \"descending\" }\n    | {\n        timestamp: \"created_time\" | \"last_edited_time\"\n        direction: \"ascending\" | \"descending\"\n      }\n  >\n  filter?:\n    | {\n        or: Array<\n          | PropertyFilter\n          | TimestampCreatedTimeFilter\n          | TimestampLastEditedTimeFilter\n          | { or: Array<PropertyFilter> }\n          | { and: Array<PropertyFilter> }\n        >\n      }\n    | {\n        and: Array<\n          | PropertyFilter\n          | TimestampCreatedTimeFilter\n          | TimestampLastEditedTimeFilter\n          | { or: Array<PropertyFilter> }\n          | { and: Array<PropertyFilter> }\n        >\n      }\n    | PropertyFilter\n    | TimestampCreatedTimeFilter\n    | TimestampLastEditedTimeFilter\n  start_cursor?: string\n  page_size?: number\n  archived?: boolean\n  in_trash?: boolean\n}\n\nexport type QueryDatabaseParameters = QueryDatabasePathParameters &\n  QueryDatabaseQueryParameters &\n  QueryDatabaseBodyParameters\n\nexport type QueryDatabaseResponse = {\n  type: \"page_or_database\"\n  page_or_database: EmptyObject\n  object: \"list\"\n  next_cursor: string | null\n  has_more: boolean\n  results: Array<\n    | PageObjectResponse\n    | PartialPageObjectResponse\n    | PartialDatabaseObjectResponse\n    | DatabaseObjectResponse\n  >\n}\n\nexport const queryDatabase = {\n  method: \"post\",\n  pathParams: [\"database_id\"],\n  queryParams: [\"filter_properties\"],\n  bodyParams: [\n    \"sorts\",\n    \"filter\",\n    \"start_cursor\",\n    \"page_size\",\n    \"archived\",\n    \"in_trash\",\n  ],\n  path: (p: QueryDatabasePathParameters): string =>\n    `databases/${p.database_id}/query`,\n} as const\n\ntype ListDatabasesQueryParameters = {\n  start_cursor?: string\n  page_size?: number\n}\n\nexport type ListDatabasesParameters = ListDatabasesQueryParameters\n\nexport type ListDatabasesResponse = {\n  type: \"database\"\n  database: EmptyObject\n  object: \"list\"\n  next_cursor: string | null\n  has_more: boolean\n  results: Array<PartialDatabaseObjectResponse | DatabaseObjectResponse>\n}\n\nexport const listDatabases = {\n  method: \"get\",\n  pathParams: [],\n  queryParams: [\"start_cursor\", \"page_size\"],\n  bodyParams: [],\n  path: (): string => `databases`,\n} as const\n\ntype CreateDatabaseBodyParameters = {\n  parent:\n    | { page_id: IdRequest; type?: \"page_id\" }\n    | { database_id: IdRequest; type?: \"database_id\" }\n  properties: Record<\n    string,\n    | {\n        number: { format?: NumberFormat }\n        type?: \"number\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        formula: { expression?: string }\n        type?: \"formula\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        select: {\n          options?: Array<{\n            name: StringRequest\n            color?: SelectColor\n            description?: StringRequest | null\n          }>\n        }\n        type?: \"select\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        multi_select: {\n          options?: Array<{\n            name: StringRequest\n            color?: SelectColor\n            description?: StringRequest | null\n          }>\n        }\n        type?: \"multi_select\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        relation:\n          | {\n              single_property: EmptyObject\n              database_id: IdRequest\n              type?: \"single_property\"\n            }\n          | {\n              dual_property: Record<string, never>\n              database_id: IdRequest\n              type?: \"dual_property\"\n            }\n        type?: \"relation\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        rollup:\n          | {\n              rollup_property_name: string\n              relation_property_name: string\n              function: RollupFunction\n              rollup_property_id?: string\n              relation_property_id?: string\n            }\n          | {\n              rollup_property_name: string\n              relation_property_id: string\n              function: RollupFunction\n              relation_property_name?: string\n              rollup_property_id?: string\n            }\n          | {\n              relation_property_name: string\n              rollup_property_id: string\n              function: RollupFunction\n              rollup_property_name?: string\n              relation_property_id?: string\n            }\n          | {\n              rollup_property_id: string\n              relation_property_id: string\n              function: RollupFunction\n              rollup_property_name?: string\n              relation_property_name?: string\n            }\n        type?: \"rollup\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        unique_id: { prefix?: string | null }\n        type?: \"unique_id\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        title: EmptyObject\n        type?: \"title\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        rich_text: EmptyObject\n        type?: \"rich_text\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        url: EmptyObject\n        type?: \"url\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        people: EmptyObject\n        type?: \"people\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        files: EmptyObject\n        type?: \"files\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        email: EmptyObject\n        type?: \"email\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        phone_number: EmptyObject\n        type?: \"phone_number\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        date: EmptyObject\n        type?: \"date\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        checkbox: EmptyObject\n        type?: \"checkbox\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        created_by: EmptyObject\n        type?: \"created_by\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        created_time: EmptyObject\n        type?: \"created_time\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        last_edited_by: EmptyObject\n        type?: \"last_edited_by\"\n        description?: PropertyDescriptionRequest | null\n      }\n    | {\n        last_edited_time: EmptyObject\n        type?: \"last_edited_time\"\n        description?: PropertyDescriptionRequest | null\n      }\n  >\n  icon?:\n    | { emoji: EmojiRequest; type?: \"emoji\" }\n    | null\n    | { external: { url: TextRequest }; type?: \"external\" }\n    | null\n    | {\n        custom_emoji: { id: IdRequest; name?: string; url?: string }\n        type?: \"custom_emoji\"\n      }\n    | null\n  cover?: { external: { url: TextRequest }; type?: \"external\" } | null\n  title?: Array<RichTextItemRequest>\n  description?: Array<RichTextItemRequest>\n  is_inline?: boolean\n}\n\nexport type CreateDatabaseParameters = CreateDatabaseBodyParameters\n\nexport type CreateDatabaseResponse =\n  | PartialDatabaseObjectResponse\n  | DatabaseObjectResponse\n\nexport const createDatabase = {\n  method: \"post\",\n  pathParams: [],\n  queryParams: [],\n  bodyParams: [\n    \"parent\",\n    \"properties\",\n    \"icon\",\n    \"cover\",\n    \"title\",\n    \"description\",\n    \"is_inline\",\n  ],\n  path: (): string => `databases`,\n} as const\n\ntype SearchBodyParameters = {\n  sort?: {\n    timestamp: \"last_edited_time\"\n    direction: \"ascending\" | \"descending\"\n  }\n  query?: string\n  start_cursor?: string\n  page_size?: number\n  filter?: { property: \"object\"; value: \"page\" | \"database\" }\n}\n\nexport type SearchParameters = SearchBodyParameters\n\nexport type SearchResponse = {\n  type: \"page_or_database\"\n  page_or_database: EmptyObject\n  object: \"list\"\n  next_cursor: string | null\n  has_more: boolean\n  results: Array<\n    | PageObjectResponse\n    | PartialPageObjectResponse\n    | PartialDatabaseObjectResponse\n    | DatabaseObjectResponse\n  >\n}\n\nexport const search = {\n  method: \"post\",\n  pathParams: [],\n  queryParams: [],\n  bodyParams: [\"sort\", \"query\", \"start_cursor\", \"page_size\", \"filter\"],\n  path: (): string => `search`,\n} as const\n\ntype CreateCommentBodyParameters =\n  | {\n      parent: { page_id: IdRequest; type?: \"page_id\" }\n      rich_text: Array<RichTextItemRequest>\n    }\n  | { discussion_id: IdRequest; rich_text: Array<RichTextItemRequest> }\n\nexport type CreateCommentParameters = CreateCommentBodyParameters\n\nexport type CreateCommentResponse =\n  | CommentObjectResponse\n  | PartialCommentObjectResponse\n\nexport const createComment = {\n  method: \"post\",\n  pathParams: [],\n  queryParams: [],\n  bodyParams: [\"parent\", \"rich_text\", \"discussion_id\"],\n  path: (): string => `comments`,\n} as const\n\ntype ListCommentsQueryParameters = {\n  block_id: IdRequest\n  start_cursor?: string\n  page_size?: number\n}\n\nexport type ListCommentsParameters = ListCommentsQueryParameters\n\nexport type ListCommentsResponse = {\n  type: \"comment\"\n  comment: EmptyObject\n  object: \"list\"\n  next_cursor: string | null\n  has_more: boolean\n  results: Array<CommentObjectResponse>\n}\n\nexport const listComments = {\n  method: \"get\",\n  pathParams: [],\n  queryParams: [\"block_id\", \"start_cursor\", \"page_size\"],\n  bodyParams: [],\n  path: (): string => `comments`,\n} as const\n\ntype OauthTokenBodyParameters = {\n  grant_type: string\n  code: string\n  redirect_uri?: string\n  external_account?: { key: string; name: string }\n}\n\nexport type OauthTokenParameters = OauthTokenBodyParameters\n\nexport type OauthTokenResponse = {\n  access_token: string\n  token_type: \"bearer\"\n  bot_id: string\n  workspace_icon: string | null\n  workspace_name: string | null\n  workspace_id: string\n  owner:\n    | {\n        type: \"user\"\n        user:\n          | {\n              type: \"person\"\n              person: { email: string }\n              name: string | null\n              avatar_url: string | null\n              id: IdRequest\n              object: \"user\"\n            }\n          | PartialUserObjectResponse\n      }\n    | { type: \"workspace\"; workspace: true }\n  duplicated_template_id: string | null\n}\n\nexport const oauthToken = {\n  method: \"post\",\n  pathParams: [],\n  queryParams: [],\n  bodyParams: [\"grant_type\", \"code\", \"redirect_uri\", \"external_account\"],\n  path: (): string => `oauth/token`,\n} as const\n\ntype OauthRevokeBodyParameters = { token: string }\n\nexport type OauthRevokeParameters = OauthRevokeBodyParameters\n\nexport type OauthRevokeResponse = Record<string, never>\n\nexport const oauthRevoke = {\n  method: \"post\",\n  pathParams: [],\n  queryParams: [],\n  bodyParams: [\"token\"],\n  path: (): string => `oauth/revoke`,\n} as const\n\ntype OauthIntrospectBodyParameters = { token: string }\n\nexport type OauthIntrospectParameters = OauthIntrospectBodyParameters\n\nexport type OauthIntrospectResponse = {\n  active: boolean\n  scope?: string\n  iat?: number\n}\n\nexport const oauthIntrospect = {\n  method: \"post\",\n  pathParams: [],\n  queryParams: [],\n  bodyParams: [\"token\"],\n  path: (): string => `oauth/introspect`,\n} as const\n"]}