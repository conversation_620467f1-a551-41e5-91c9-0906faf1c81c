/// <reference types="node" />
/// <reference types="node" />
import type { Agent } from "node:http";
import { type Logger, LogLevel } from "./logging";
import { type GetBlockParameters, type GetBlockResponse, type UpdateBlockParameters, type UpdateBlockResponse, type DeleteBlockParameters, type DeleteBlockResponse, type AppendBlockChildrenParameters, type AppendBlockChildrenResponse, type ListBlockChildrenParameters, type ListBlockChildrenResponse, type ListDatabasesParameters, type ListDatabasesResponse, type GetDatabaseParameters, type GetDatabaseResponse, type QueryDatabaseParameters, type QueryDatabaseResponse, type CreateDatabaseParameters, type CreateDatabaseResponse, type UpdateDatabaseParameters, type UpdateDatabaseResponse, type CreatePageParameters, type CreatePageResponse, type GetPageParameters, type GetPageResponse, type UpdatePageParameters, type UpdatePageResponse, type GetUserParameters, type GetUserResponse, type ListUsersParameters, type ListUsersResponse, type SearchParameters, type SearchResponse, type GetSelfParameters, type GetSelfResponse, type GetPagePropertyParameters, type GetPagePropertyResponse, type CreateCommentParameters, type CreateCommentResponse, type ListCommentsParameters, type ListCommentsResponse, type OauthTokenResponse, type OauthTokenParameters, type OauthIntrospectResponse, type OauthIntrospectParameters, type OauthRevokeResponse, type OauthRevokeParameters } from "./api-endpoints";
import type { SupportedFetch } from "./fetch-types";
export interface ClientOptions {
    auth?: string;
    timeoutMs?: number;
    baseUrl?: string;
    logLevel?: LogLevel;
    logger?: Logger;
    notionVersion?: string;
    fetch?: SupportedFetch;
    /** Silently ignored in the browser */
    agent?: Agent;
}
export interface RequestParameters {
    path: string;
    method: Method;
    query?: QueryParams;
    body?: Record<string, unknown>;
    /**
     * To authenticate using public API token, `auth` should be passed as a
     * string. If you are trying to complete OAuth, then `auth` should be an object
     * containing your integration's client ID and secret.
     */
    auth?: string | {
        client_id: string;
        client_secret: string;
    };
}
export default class Client {
    #private;
    static readonly defaultNotionVersion = "2022-06-28";
    constructor(options?: ClientOptions);
    /**
     * Sends a request.
     *
     * @param path
     * @param method
     * @param query
     * @param body
     * @returns
     */
    request<ResponseBody>({ path, method, query, body, auth, }: RequestParameters): Promise<ResponseBody>;
    readonly blocks: {
        /**
         * Retrieve block
         */
        retrieve: (args: WithAuth<GetBlockParameters>) => Promise<GetBlockResponse>;
        /**
         * Update block
         */
        update: (args: WithAuth<UpdateBlockParameters>) => Promise<UpdateBlockResponse>;
        /**
         * Delete block
         */
        delete: (args: WithAuth<DeleteBlockParameters>) => Promise<DeleteBlockResponse>;
        children: {
            /**
             * Append block children
             */
            append: (args: WithAuth<AppendBlockChildrenParameters>) => Promise<AppendBlockChildrenResponse>;
            /**
             * Retrieve block children
             */
            list: (args: WithAuth<ListBlockChildrenParameters>) => Promise<ListBlockChildrenResponse>;
        };
    };
    readonly databases: {
        /**
         * List databases
         *
         * @deprecated Please use `search`
         */
        list: (args: WithAuth<ListDatabasesParameters>) => Promise<ListDatabasesResponse>;
        /**
         * Retrieve a database
         */
        retrieve: (args: WithAuth<GetDatabaseParameters>) => Promise<GetDatabaseResponse>;
        /**
         * Query a database
         */
        query: (args: WithAuth<QueryDatabaseParameters>) => Promise<QueryDatabaseResponse>;
        /**
         * Create a database
         */
        create: (args: WithAuth<CreateDatabaseParameters>) => Promise<CreateDatabaseResponse>;
        /**
         * Update a database
         */
        update: (args: WithAuth<UpdateDatabaseParameters>) => Promise<UpdateDatabaseResponse>;
    };
    readonly pages: {
        /**
         * Create a page
         */
        create: (args: WithAuth<CreatePageParameters>) => Promise<CreatePageResponse>;
        /**
         * Retrieve a page
         */
        retrieve: (args: WithAuth<GetPageParameters>) => Promise<GetPageResponse>;
        /**
         * Update page properties
         */
        update: (args: WithAuth<UpdatePageParameters>) => Promise<UpdatePageResponse>;
        properties: {
            /**
             * Retrieve page property
             */
            retrieve: (args: WithAuth<GetPagePropertyParameters>) => Promise<GetPagePropertyResponse>;
        };
    };
    readonly users: {
        /**
         * Retrieve a user
         */
        retrieve: (args: WithAuth<GetUserParameters>) => Promise<GetUserResponse>;
        /**
         * List all users
         */
        list: (args: WithAuth<ListUsersParameters>) => Promise<ListUsersResponse>;
        /**
         * Get details about bot
         */
        me: (args: WithAuth<GetSelfParameters>) => Promise<GetSelfResponse>;
    };
    readonly comments: {
        /**
         * Create a comment
         */
        create: (args: WithAuth<CreateCommentParameters>) => Promise<CreateCommentResponse>;
        /**
         * List comments
         */
        list: (args: WithAuth<ListCommentsParameters>) => Promise<ListCommentsResponse>;
    };
    /**
     * Search
     */
    search: (args: WithAuth<SearchParameters>) => Promise<SearchResponse>;
    readonly oauth: {
        /**
         * Get token
         */
        token: (args: OauthTokenParameters & {
            client_id: string;
            client_secret: string;
        }) => Promise<OauthTokenResponse>;
        /**
         * Introspect token
         */
        introspect: (args: OauthIntrospectParameters & {
            client_id: string;
            client_secret: string;
        }) => Promise<OauthIntrospectResponse>;
        /**
         * Revoke token
         */
        revoke: (args: OauthRevokeParameters & {
            client_id: string;
            client_secret: string;
        }) => Promise<OauthRevokeResponse>;
    };
    /**
     * Emits a log message to the console.
     *
     * @param level The level for this message
     * @param args Arguments to send to the console
     */
    private log;
    /**
     * Transforms an API key or access token into a headers object suitable for an HTTP request.
     *
     * This method uses the instance's value as the default when the input is undefined. If neither are defined, it returns
     * an empty object
     *
     * @param auth API key or access token
     * @returns headers key-value object
     */
    private authAsHeaders;
}
type Method = "get" | "post" | "patch" | "delete";
type QueryParams = Record<string, string | number | string[]> | URLSearchParams;
type WithAuth<P> = P & {
    auth?: string;
};
export {};
//# sourceMappingURL=Client.d.ts.map