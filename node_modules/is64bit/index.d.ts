/**
Check whether operating system CPU architecture is 64-bit or 32-bit.

@example
```
import {is64bit} from 'is64bit';

// On ARM64 macOS
console.log(await is64bit());
//=> true
```
*/
export function is64bit(): Promise<boolean>;

/**
Check whether operating system CPU architecture is 64-bit or 32-bit.

**Note**: Prefer the async version for browser or cross-platform usage as it has a more reliable check.

@example
```
import {is64bitSync} from 'is64bit';

// On ARM64 macOS
console.log(is64bitSync());
//=> true
```
*/
export function is64bitSync(): boolean;
