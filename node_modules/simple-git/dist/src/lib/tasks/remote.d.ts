import { StringTask } from '../types';
export declare function addRemoteTask(remoteName: string, remoteRepo: string, customArgs: string[]): StringTask<string>;
export declare function getRemotesTask(verbose: boolean): StringTask<any>;
export declare function listRemotesTask(customArgs: string[]): StringTask<string>;
export declare function remoteTask(customArgs: string[]): StringTask<string>;
export declare function removeRemoteTask(remoteName: string): StringTask<string>;
