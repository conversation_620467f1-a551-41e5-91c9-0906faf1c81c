[user]
name = <PERSON>
email = <EMAIL>
signingkey = 4B177344822BD3BB

[github]
user = solnic

[commit]
gpgsign = true

[color]
branch = auto
diff = auto
status = auto

[color "branch"]
current = yellow reverse
local = yellow
remote = green

[color "diff"]
meta = yellow bold
frag = magenta bold
old = red bold
new = green bold

[color "status"]
added = yellow
changed = green
untracked = cyan

[color]
ui = true

[core]
whitespace=fix,-indent-with-non-tab,trailing-space,cr-at-eol
excludesfile = ~/.gitignore
trustctime = true

[alias]
st = status
c = commit
cm = commit -am
br = branch
co = checkout
df = diff
lg = log -p
aa = add .
p = push
pf = push -f
u = up
rc = rebase --continue
rem = rebase master
remi = rebase master -i
uchl = commit -am 'Update CHANGELOG [ci skip]'
urdm = commit -am 'Update README [ci skip]'
msf = commit -am 'Minor style fixes'
wip = commit -am 'WIP'
lod = log --oneline --decorate
sed = !sh -c 'git ls-files -z $2 | xargs -0 sed -i -e $1' -
rbm = !zsh -c 'git checkout $GIT_MAIN_BRANCH && git pull && git checkout - && git rebase $GIT_MAIN_BRANCH' -

[push]
default = upstream

[diff]
compactionHeuristic = true
noprefix = true

[filter "lfs"]
clean = git-lfs clean -- %f
smudge = git-lfs smudge -- %f
process = git-lfs filter-process
required = true

[diff]
tool = default-difftool

[difftool "default-difftool"]
cmd = code --wait --diff $LOCAL $REMOTE
