# Test runner that automatically detects project type and runs appropriate test command
t() {
  local test_command=""
  local test_args=""

  # Build test arguments from input parameters
  for arg in "$@"; do
    test_args+=" $arg"
  done

  # Detect project type
  if [ -f "mix.exs" ]; then
    # Elixir project
    test_command="mix test"
  elif [ -f "Gemfile" ]; then
    # Ruby project
    test_command="bundle exec rspec"
  else
    echo "Error: No mix.exs or Gemfile found. Cannot determine project type."
    return 1
  fi

  # Run the tests with any provided arguments
  eval "${test_command}${test_args}"
}

# Function to determine which editor to use based on project type
get_git_editor() {
  local editor="code"

  # First check if we're in a devcontainer/remote environment
  if [ -n "$REMOTE_CONTAINERS" ] || [ -n "$CODESPACES" ] || [ -n "$DEVCONTAINER_CLI" ]; then
    # In a devcontainer, always use code
    editor="code"
  else
    # Check for project-specific editor configuration
    if [ -f ".editor-config" ]; then
      # Read the first line of the .editor-config file
      local config_editor=$(head -n 1 .editor-config)
      if [ "$config_editor" = "cursor" ] || [ "$config_editor" = "code" ]; then
        editor="$config_editor"
      fi
    else
      # Auto-detect based on project files
      # Add your project-specific detection logic here
      # For example, use cursor for Elixir projects and code for Ruby projects
      if [ -f "mix.exs" ]; then
        # Elixir project - check if cursor is available
        if command -v cursor >/dev/null 2>&1; then
          editor="cursor"
        fi
      elif [ -f "Gemfile" ]; then
        # Ruby project - use VS Code
        editor="code"
      fi
    fi
  fi

  # Verify the editor is actually available
  if ! command -v $editor >/dev/null 2>&1; then
    # Fallback to code if the selected editor isn't available
    if command -v code >/dev/null 2>&1; then
      editor="code"
    # If code isn't available either, try vim or nano
    elif command -v vim >/dev/null 2>&1; then
      editor="vim"
    elif command -v nano >/dev/null 2>&1; then
      editor="nano"
    fi
  fi

  echo "$editor --wait"
}

# Function to update GIT_EDITOR when changing directories
update_git_editor() {
  export GIT_EDITOR="$(get_git_editor)"
}

# Add the update_git_editor function to the chpwd hook
autoload -U add-zsh-hook
add-zsh-hook chpwd update_git_editor

# Initialize GIT_EDITOR on shell startup
update_git_editor
