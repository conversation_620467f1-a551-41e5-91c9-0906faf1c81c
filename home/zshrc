export PATH="$HOME/.bin:$PATH"
export ZSH="$HOME/.oh-my-zsh"

ZSH_THEME="gozilla"
DISABLE_AUTO_UPDATE="true"

# Default zsh plugins
plugins=(git)

if [[ $(os) == "MacOS" ]]; then
  plugins+=(bundler chruby mix-fast)
  export PATH=$HOME/opt/homebrew/Cellar/erlang/27.1.2/lib/erlang/erts-15.1.2/bin:$PATH
  export PATH=$HOME/opt/homebrew/bin:$PATH
else
  plugins+=(bundler mix-fast)
fi

source $ZSH/oh-my-zsh.sh

for file in $(ls $HOME/.zsh); do
  source $HOME/.zsh/$file
done

source $HOME/.env

export GIT_MAIN_BRANCH=$(git_main_branch)
