# zsh is my shell
set -g default-command /bin/zsh
set -g default-shell /bin/zsh

set-window-option -g xterm-keys on

# fixes issues with iterm and sending hex codes
set -g assume-paste-time 0

# fixes ESC delay in emacs
set -s escape-time 0

# look nice
set -g default-terminal "screen-256color"

# don't allow the terminal to rename windows
set-window-option -g allow-rename off

# status bar
set -g status-interval 60
set -g status-left-length 40
set -g status-left "#[bg=colour66] #[fg=black]#S #[fg=white]#I #[fg=white]#P "
set -g status-right "#[fg=cyan] %d %b %R "
set -g status-justify centre

# display activity from other windows
setw -g monitor-activity off
set -g visual-activity off

# status bar colors
set -g status-fg colour247
set -g status-bg black

# regular window colors
setw -g window-status-fg default
setw -g window-status-bg default
setw -g window-status-attr dim

# current window colors
setw -g window-status-current-fg colour250
setw -g window-status-current-bg black
setw -g window-status-current-attr bright

# pane border colors
set -g pane-border-fg green
set -g pane-border-bg default
set -g pane-active-border-fg green
set -g pane-active-border-bg default

# command line colors
set -g message-bg colour39
set -g message-fg black

# vim bindings in copy mode
setw -g mode-keys vi

# switch prefix key to ctrl+a and turn off default ctrl+b binding
set -g prefix2 C-a
bind C-a send-prefix

# reload the conf on prefix+r
bind r source-file ~/.tmux.conf \; display "Configuration Reloaded!"

# new-window binding
bind -n C-t new-window

bind L next-window

# easy split window bindings
# bind S split-window -h
# bind V split-window -v

# use hjkl to move between panes
bind h select-pane -L
bind j select-pane -D
bind k select-pane -U
bind l select-pane -R

# easy pane resize
bind -r C-h resize-pane -L 5
bind -r C-j resize-pane -D 5
bind -r C-k resize-pane -U 5
bind -r C-l resize-pane -R 5

# source local conf
source-file ~/.tmux.conf.local
source-file ~/.tmux.colors

#set-option -g default-command "reattach-to-user-namespace -l $SHELL"
