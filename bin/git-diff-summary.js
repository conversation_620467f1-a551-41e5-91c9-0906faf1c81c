#!/usr/bin/env node

/**
 * Git Diff Technical Summarizer
 *
 * This script:
 * 1. Gets diffs from a specified repository for today (or a specified date range)
 * 2. Analyzes the diffs to extract technical changes
 * 3. Uses an LLM API to generate a technical summary highlighting interesting solutions and libraries
 * 4. Copies the summary to the clipboard and outputs it to the console
 *
 * Usage: bin/gc diff-summary <repo-name> [--author "Author Name"] [--start-date YYYY-MM-DD] [--end-date YYYY-MM-DD] [-b|--backend <provider>]
 *
 * Requirements:
 * - Node.js
 * - simple-git package (for Git operations)
 * - pbcopy (for clipboard operations on macOS)
 *
 * Setup:
 * 1. Set appropriate API key environment variable based on the LLM provider:
 *    - ANTHROPIC_API_KEY or CLAUDE_API_KEY for Anthropic Claude
 *    - OPENAI_API_KEY for OpenAI
 *    - LOCAL_LLM_API_KEY for local LLM Studio (optional)
 * 2. Use -b or --backend option to specify the LLM provider:
 *    - 'anthropic' (default)
 *    - 'openai'
 *    - 'local'
 */

const simpleGit = require('simple-git');
const path = require('path');
const fs = require('fs');
const os = require('os');
const { execSync } = require('child_process');
const llm = require('../lib/llm');

function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    repoName: null,
    author: null,
    startDate: null,
    endDate: null,
    backend: process.env.LLM_PROVIDER
  };

  // Parse arguments
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    // Handle options with values
    if (arg === '--author' && i + 1 < args.length) {
      options.author = args[i + 1];
      i++;
    } else if (arg === '--start-date' && i + 1 < args.length) {
      options.startDate = args[i + 1];
      i++;
    } else if (arg === '--end-date' && i + 1 < args.length) {
      options.endDate = args[i + 1];
      i++;
    } else if ((arg === '-b' || arg === '--backend') && i + 1 < args.length) {
      options.backend = args[i + 1];
      i++;
    }
    // Handle positional arguments (repo name)
    else if (!options.repoName && !arg.startsWith('-')) {
      options.repoName = arg;
    }
  }

  // Check if repo name is provided
  if (!options.repoName) {
    console.error('Error: Repository name is required');
    console.error('Usage: bin/gc diff-summary <repo-name> [--author "Author Name"] [--start-date YYYY-MM-DD] [--end-date YYYY-MM-DD] [-b|--backend <provider>]');
    console.error('Available backends: anthropic, openai, local');
    process.exit(1);
  }

  return options;
}

// Calculate default date range (just today)
function calculateDateRange() {
  // Get today's date in local time
  const today = new Date();

  // Format today's date as YYYY-MM-DD
  const todayStr = today.toISOString().split('T')[0];

  // Use today for both start and end date
  const startDate = todayStr;
  const endDate = todayStr;

  console.log(`Using date: ${todayStr}`);

  return { startDate, endDate };
}

// Get the full path to the repository and validate it
function getRepoPath(repoName) {
  const repoPath = path.join(os.homedir(), 'Workspace', repoName);

  // Check if the path exists
  if (!fs.existsSync(repoPath)) {
    console.error(`Error: Repository path not found: ${repoPath}`);
    process.exit(1);
  }

  // Check if it's a git repository
  const gitDir = path.join(repoPath, '.git');
  if (!fs.existsSync(gitDir)) {
    console.error(`Error: Not a git repository: ${repoPath}`);
    console.error(`The .git directory was not found at: ${gitDir}`);
    process.exit(1);
  }

  return repoPath;
}

// Get diffs from the repository
async function getDiffs(repoPath, author, startDate, endDate) {
  try {
    console.log(`Fetching diffs from repository at: ${repoPath}`);

    // Initialize simple-git with the repository path
    const git = simpleGit({ baseDir: repoPath });

    // Verify that git is working in this repository
    try {
      await git.status();
    } catch (error) {
      console.error(`Error: Unable to get git status in ${repoPath}`);
      console.error(`This may not be a valid git repository or git may not be installed.`);
      process.exit(1);
    }

    // Build the git command arguments for getting commit hashes in the date range
    let logArgs = ['--no-pager', 'log', '--pretty=format:%H'];

    // Handle date range
    if (startDate === endDate) {
      // If start and end dates are the same, use --date=<date>
      logArgs.push(`--since=${startDate}T00:00:00`, `--until=${endDate}T23:59:59`);
    } else {
      // Otherwise use normal date range
      logArgs.push(`--after=${startDate}`, `--before=${endDate}`);
    }

    // Add author filter if specified
    if (author) {
      logArgs.push(`--author=${author}`);
    }

    console.log(`Running git command: git ${logArgs.join(' ')}`);

    // Execute the git command to get commit hashes
    const commitHashes = await git.raw(logArgs);

    // Split the output into individual commit hashes
    const commits = commitHashes.split('\n').filter(line => line.trim() !== '');

    if (commits.length === 0) {
      return { diffs: [], files: [] };
    }

    console.log(`Found ${commits.length} commits. Fetching diffs...`);

    // Get diffs for each commit
    let allDiffs = '';
    let changedFiles = new Set();

    for (const commit of commits) {
      // Get the diff for this commit
      const diffArgs = ['show', '--patch', '--stat', commit];
      const diff = await git.raw(diffArgs);

      // Extract changed files from the diff
      const fileRegex = /\|\s+\d+\s+[+-]+$/gm;
      const fileLines = diff.match(fileRegex);

      if (fileLines) {
        // Extract filenames from the file lines
        const fileNameRegex = /^(.+?)\s+\|/;
        fileLines.forEach(line => {
          const match = line.match(fileNameRegex);
          if (match && match[1]) {
            changedFiles.add(match[1].trim());
          }
        });
      }

      allDiffs += diff + '\n\n';
    }

    // Convert Set to Array
    const filesArray = Array.from(changedFiles);

    return { diffs: allDiffs, files: filesArray };
  } catch (error) {
    console.error(`Error getting diffs: ${error.message}`);
    process.exit(1);
  }
}

// Generate a technical summary using the configured LLM provider
async function generateTechnicalSummary(diffs, files, backend = 'anthropic') {
  try {
    if (!diffs || diffs.length === 0) {
      return 'No diffs found in the specified date range.';
    }

    // Create a custom config with the specified backend
    const config = {
      provider: backend,
      ...llm.getConfigFromEnv() // Get other config values from environment
    };

    // Override the provider with the one specified
    config.provider = backend;

    const model = llm.create(config);

    console.log(`Using LLM provider: ${config.provider}`);

    // Truncate diffs if they're too large (LLMs have token limits)
    const maxDiffLength = 50000; // Adjust as needed
    const truncatedDiffs = diffs.length > maxDiffLength
      ? diffs.substring(0, maxDiffLength) + '...[truncated due to size]'
      : diffs;

    const prompt = `Analyze these git diffs and provide a technical summary highlighting:
    1. Key technical changes
    2. Interesting technical solutions or patterns
    3. Libraries or frameworks being used
    4. Architecture changes or design patterns
    5. Performance improvements or optimizations

    Format your response as a concise bullet-point summary with technical details.
    Focus on the most significant technical aspects, not trivial changes.

    Changed files:
    ${files.join('\n')}

    Git diffs:
    ${truncatedDiffs}`;

    const summary = await model.generateText({
      prompt,
      maxTokens: 1500,
      temperature: 0.2
    });

    return summary;
  } catch (error) {
    console.error(`Error generating technical summary with ${backend}:`);
    if (error.response) {
      console.error('API response:', error.response.data);
    } else {
      console.error(error.message);
    }
    process.exit(1);
  }
}

// Copy summary to clipboard
async function copyToClipboard(repoName, summary) {
  const formattedSummary = `${repoName} Technical Summary:\n\n${summary}`;

  try {
    // Use pbcopy on macOS to copy to clipboard
    execSync('pbcopy', { input: formattedSummary });
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error.message);
    return false;
  }
}

// Main function
async function main() {
  try {
    // Parse command line arguments
    const options = parseArgs();

    // Calculate date range if not specified
    const dateRange = calculateDateRange();
    const startDate = options.startDate || dateRange.startDate;
    const endDate = options.endDate || dateRange.endDate;

    // Get and validate repository path (will exit if invalid)
    const repoPath = getRepoPath(options.repoName);

    // If start and end dates are the same, just say "for <date>"
    if (startDate === endDate) {
      console.log(`Analyzing diffs in ${options.repoName} for ${startDate}...`);
    } else {
      console.log(`Analyzing diffs in ${options.repoName} from ${startDate} to ${endDate}...`);
    }

    // Get diffs
    const { diffs, files } = await getDiffs(repoPath, options.author, startDate, endDate);

    if (!diffs || diffs.length === 0) {
      console.log('No diffs found in the specified date range.');
      process.exit(0);
    }

    console.log(`Found diffs in ${files.length} files. Generating technical summary...`);
    console.log(`Using backend: ${options.backend}`);

    // Generate technical summary with the specified backend
    const summary = await generateTechnicalSummary(diffs, files, options.backend);

    // Format the summary with the repo name
    const formattedSummary = `${options.repoName} Technical Summary:\n\n${summary}`;

    // Copy to clipboard
    const copied = await copyToClipboard(options.repoName, summary);

    if (copied) {
      console.log('Technical summary copied to clipboard!');
    }

    // Output to stdout
    console.log('\nTechnical Summary:');
    console.log(formattedSummary);

  } catch (error) {
    console.error('Error:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
    process.exit(1);
  }
}

// Run the main function
main();
