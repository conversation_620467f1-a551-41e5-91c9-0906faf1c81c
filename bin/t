#!/usr/bin/env zsh

# Test runner that automatically detects project type and runs appropriate test command
local test_command=""
local test_args=""

# Build test arguments from input parameters
for arg in "$@"; do
    test_args+=" $arg"
done

# Detect project type
if [ -f "mix.exs" ]; then
    # Elixir project
    test_command="mix test"
elif [ -f "Gemfile" ]; then
    # Ruby project
    test_command="bundle exec rspec"
else
    echo "Error: No mix.exs or Gemfile found. Cannot determine project type."
    exit 1
fi

# Run the tests with any provided arguments
eval "${test_command}${test_args}"
