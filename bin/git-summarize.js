#!/usr/bin/env node

/**
 * Git Commit Summarizer
 *
 * This script:
 * 1. Gets commits from a specified repository for today (or a specified date range)
 * 2. Detects the current branch and checks if it's related to a GitHub issue
 * 3. If on a feature branch, only includes commits specific to that branch (not in main/master)
 * 4. Uses an LLM API to generate a summary of the commits
 * 5. Includes a link to the GitHub issue if detected from the branch name
 * 6. Copies the summary to the clipboard and outputs it to the console
 *
 * Branch name detection:
 * - If the branch name starts with a number (e.g., "123-feature-description"),
 *   it's considered a GitHub issue number
 * - If the branch name starts with "issue-", "bug-", "feature-", or "fix-" followed by a number,
 *   it's also considered a GitHub issue number
 *
 * GitHub repository URL detection:
 * - If the repository name has no prefix (e.g., "my-project"), it's assumed to be a personal project
 *   and the URL will be "https://github.com/solnic/my-project"
 * - If the repository name has a prefix (e.g., "org/repo"), it's used as is
 *   and the URL will be "https://github.com/org/repo"
 *
 * Usage: bin/git summarize <repo-name> [--author "Author Name"] [--start-date YYYY-MM-DD] [--end-date YYYY-MM-DD] [-b|--backend <provider>]
 *
 * Requirements:
 * - Node.js
 * - simple-git package (for Git operations)
 * - pbcopy (for clipboard operations on macOS)
 *
 * Setup:
 * 1. Set appropriate API key environment variable based on the LLM provider:
 *    - ANTHROPIC_API_KEY or CLAUDE_API_KEY for Anthropic Claude
 *    - OPENAI_API_KEY for OpenAI
 *    - LOCAL_LLM_API_KEY for local LLM Studio (optional)
 * 2. Set LLM_PROVIDER environment variable to choose the provider:
 *    - 'anthropic' (default)
 *    - 'openai'
 *    - 'local'
 */

const simpleGit = require('simple-git');
const path = require('path');
const fs = require('fs');
const os = require('os');
const { execSync } = require('child_process');
const llm = require('../lib/llm');

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    repoName: null,
    author: null,
    startDate: null,
    endDate: null,
    backend: process.env.LLM_PROVIDER || 'anthropic' // Default to anthropic if not specified
  };

  // Parse arguments
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    // Handle options with values
    if (arg === '--author' && i + 1 < args.length) {
      options.author = args[i + 1];
      i++;
    } else if (arg === '--start-date' && i + 1 < args.length) {
      options.startDate = args[i + 1];
      i++;
    } else if (arg === '--end-date' && i + 1 < args.length) {
      options.endDate = args[i + 1];
      i++;
    } else if ((arg === '-b' || arg === '--backend') && i + 1 < args.length) {
      options.backend = args[i + 1];
      i++;
    }
    // Handle positional arguments (repo name)
    else if (!options.repoName && !arg.startsWith('-')) {
      options.repoName = arg;
    }
  }

  // Check if repo name is provided
  if (!options.repoName) {
    console.error('Error: Repository name is required');
    console.error('Usage: bin/gc summarize <repo-name> [--author "Author Name"] [--start-date YYYY-MM-DD] [--end-date YYYY-MM-DD] [-b|--backend <provider>]');
    console.error('Available backends: anthropic, openai, local');
    process.exit(1);
  }

  return options;
}

// Calculate default date range (just today)
function calculateDateRange() {
  // Get today's date in local time
  const today = new Date();

  // Format today's date as YYYY-MM-DD
  const todayStr = today.toISOString().split('T')[0];

  // Use today for both start and end date
  const startDate = todayStr;
  const endDate = todayStr;

  console.log(`Using date: ${todayStr}`);

  return { startDate, endDate };
}

// Get the full path to the repository and validate it
function getRepoPath(repoName) {
  let repoPath;

  // Check if repoName is an absolute path
  if (path.isAbsolute(repoName)) {
    repoPath = repoName;
  } else {
    // Otherwise, assume it's relative to ~/Workspace
    repoPath = path.join(os.homedir(), 'Workspace', repoName);
  }

  // Check if the path exists
  if (!fs.existsSync(repoPath)) {
    console.error(`Error: Repository path not found: ${repoPath}`);
    process.exit(1);
  }

  // Check if it's a git repository
  const gitDir = path.join(repoPath, '.git');
  if (!fs.existsSync(gitDir)) {
    console.error(`Error: Not a git repository: ${repoPath}`);
    console.error(`The .git directory was not found at: ${gitDir}`);
    process.exit(1);
  }

  return repoPath;
}

// Get the current branch name
async function getCurrentBranch(git) {
  try {
    const branchSummary = await git.branch();
    return branchSummary.current;
  } catch (error) {
    console.error(`Error getting current branch: ${error.message}`);
    return null;
  }
}

// Parse branch name to extract GitHub issue number
function parseIssueNumber(branchName) {
  // Match patterns like "123-feature-description" or "issue-123-description"
  const numericPrefix = /^(\d+)[-_]/;
  const issuePattern = /^(?:issue|bug|feature|fix)[-_]?(\d+)[-_]/i;

  let match = branchName.match(numericPrefix);
  if (match) {
    return match[1];
  }

  match = branchName.match(issuePattern);
  if (match) {
    return match[1];
  }

  return null;
}

// Determine GitHub repository URL based on repository name
function getGitHubRepoUrl(repoName) {
  // Check if the repository path contains an organization name
  const pathParts = repoName.split('/');
  const repoNameOnly = path.basename(repoName);

  // If the path has a structure like "/path/to/org/repo", use "org/repo"
  if (pathParts.length >= 2) {
    const org = pathParts[pathParts.length - 2];
    const repo = pathParts[pathParts.length - 1];

    // Check if the org part looks like a valid GitHub org name (not a path component like "tmp" or "Users")
    const invalidOrgNames = ['tmp', 'temp', 'private', 'var', 'usr', 'Users', 'home', 'Workspace'];
    if (!invalidOrgNames.includes(org)) {
      return `${org}/${repo}`;
    }
  }

  // If the repository name itself contains a slash (like "org/repo"), use it as is
  if (repoNameOnly.indexOf('/') !== -1) {
    return repoNameOnly;
  }

  // Otherwise, assume it's a personal project
  return `solnic/${repoNameOnly}`;
}

// Get the main branch name (main or master)
async function getMainBranch(git) {
  try {
    // Check if 'main' branch exists
    const branches = await git.branch();
    const branchNames = Object.keys(branches.branches);

    if (branchNames.includes('main')) {
      return 'main';
    } else if (branchNames.includes('master')) {
      return 'master';
    }

    // If neither main nor master exists, return the current branch
    return branches.current;
  } catch (error) {
    console.error(`Error determining main branch: ${error.message}`);
    return null;
  }
}

// Get commits from the repository
async function getCommits(repoPath, author, startDate, endDate, gitInstance = null) {
  try {
    console.log(`Fetching commits from repository at: ${repoPath}`);

    // Use provided git instance or initialize a new one
    const git = gitInstance || simpleGit({ baseDir: repoPath });

    // Verify that git is working in this repository
    try {
      await git.status();
    } catch (error) {
      console.error(`Error: Unable to get git status in ${repoPath}`);
      console.error(`This may not be a valid git repository or git may not be installed.`);
      process.exit(1);
    }

    // Get current branch and main branch
    const currentBranch = await getCurrentBranch(git);
    const mainBranch = await getMainBranch(git);

    // Build the git command arguments
    let args = ['--no-pager', 'log', '--pretty=format:%s'];

    // If we're on a feature branch (not main/master), only include commits specific to this branch
    if (currentBranch && mainBranch &&
      currentBranch !== 'main' && currentBranch !== 'master' &&
      (mainBranch === 'main' || mainBranch === 'master')) {
      console.log(`On feature branch '${currentBranch}', filtering commits not in '${mainBranch}'`);
      args.push(`${mainBranch}..${currentBranch}`);
    }

    // Handle date range
    if (startDate === endDate) {
      // If start and end dates are the same, use --date=<date>
      args.push(`--since=${startDate}T00:00:00`, `--until=${endDate}T23:59:59`);
    } else {
      // Otherwise use normal date range
      args.push(`--after=${startDate}`, `--before=${endDate}`);
    }

    // Add author filter if specified
    if (author) {
      args.push(`--author=${author}`);
    }

    console.log(`Running git command: git ${args.join(' ')}`);

    // Execute the git command directly
    const result = await git.raw(args);

    // Split the output into individual commit messages
    const commits = result.split('\n').filter(line => line.trim() !== '');

    return commits;
  } catch (error) {
    console.error(`Error getting commits: ${error.message}`);
    process.exit(1);
  }
}

// Generate a summary using the configured LLM provider
async function generateSummary(commits, backend) {
  try {
    if (commits.length === 0) {
      return 'No commits found in the specified date range.';
    }

    // Create a custom config with the specified backend
    const config = {
      provider: backend,
      ...llm.getConfigFromEnv() // Get other config values from environment
    };

    // Override the provider with the one specified in the command line
    config.provider = backend;

    const model = llm.create(config);

    console.log(`Using LLM provider: ${config.provider}`);

    const prompt = `Analyze these commit messages and identify key technical themes.
    Provide a concise bullet-point summary for each commit:
    - [Theme]: Brief summary of the commit message (#PR_ID)

    Examples:
    - [Bugfix] Fix handling of cron with TZ in Cron::Job (#2530)
    - [Feature] Introduce Configuration#validate (#2537)

    Commit messages:
    ${commits.join('\n')}`;

    const summary = await model.generateText({
      prompt,
      maxTokens: 1000,
      temperature: 0.2
    });

    return summary;
  } catch (error) {
    console.error(`Error generating summary with ${backend}:`);
    if (error.response) {
      console.error('API response:', error.response.data);
    } else {
      console.error(error.message);
    }
    process.exit(1);
  }
}

// Copy summary to clipboard
async function copyToClipboard(repoName, summary, issueLink = null) {
  let formattedSummary;

  if (issueLink) {
    formattedSummary = `## ${repoName}\n\n- ${issueLink}\n${summary.split('\n').map(line => `  ${line}`).join('\n')}`;
  } else {
    formattedSummary = `## ${repoName}\n\n${summary}`;
  }

  try {
    // Use pbcopy on macOS to copy to clipboard
    execSync('pbcopy', { input: formattedSummary });
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error.message);
    return false;
  }
}

// Main function
async function main() {
  try {
    // Parse command line arguments
    const options = parseArgs();

    // Calculate date range if not specified
    const dateRange = calculateDateRange();
    const startDate = options.startDate || dateRange.startDate;
    const endDate = options.endDate || dateRange.endDate;

    // Get and validate repository path (will exit if invalid)
    const repoPath = getRepoPath(options.repoName);

    // Initialize git
    const git = simpleGit({ baseDir: repoPath });

    // Get current branch name
    const branchName = await getCurrentBranch(git);
    console.log(`Current branch: ${branchName}`);

    // Check if branch name contains an issue number
    let issueNumber = null;
    let issueLink = null;

    if (branchName) {
      issueNumber = parseIssueNumber(branchName);
      if (issueNumber) {
        // Determine GitHub repository URL
        const githubRepo = getGitHubRepoUrl(options.repoName);
        issueLink = `https://github.com/${githubRepo}/issues/${issueNumber}`;
        console.log(`Detected GitHub issue: ${issueLink}`);
      }
    }

    // If start and end dates are the same, just say "for <date>"
    if (startDate === endDate) {
      console.log(`Analyzing commits in ${options.repoName} for ${startDate}...`);
    } else {
      console.log(`Analyzing commits in ${options.repoName} from ${startDate} to ${endDate}...`);
    }

    // Get commits (now async)
    const commits = await getCommits(repoPath, options.author, startDate, endDate, git);

    if (commits.length === 0) {
      console.log('No commits found in the specified date range.');
      process.exit(0);
    }

    console.log(`Found ${commits.length} commits. Generating summary...`);
    console.log(`Using backend: ${options.backend}`);

    // Generate summary with the specified backend
    const summary = await generateSummary(commits, options.backend);

    // Format the summary with the repo name and issue link if available
    let formattedSummary;
    if (issueLink) {
      formattedSummary = `## ${options.repoName}\n\n- ${issueLink}\n${summary.split('\n').map(line => `  ${line}`).join('\n')}`;
    } else {
      formattedSummary = `## ${options.repoName}\n\n${summary}`;
    }

    // Copy to clipboard
    const copied = await copyToClipboard(options.repoName, summary, issueLink);

    if (copied) {
      console.log('Summary copied to clipboard!');
    }

    // Output to stdout
    console.log('\nSummary:');
    console.log(formattedSummary);

  } catch (error) {
    console.error('Error:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
    process.exit(1);
  }
}

// Run the main function
main();
