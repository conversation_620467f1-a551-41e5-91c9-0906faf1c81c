#!/usr/bin/env node

/**
 * Notion Daily Log Appender
 *
 * This script reads text from the clipboard, parses it as Markdown, and appends it
 * to a Notion "Daily log" page created on the current day with proper styling.
 *
 * Requirements:
 * - Node.js
 * - @notionhq/client package
 * - clipboardy package
 * - marked package (for Markdown parsing)
 *
 * Setup:
 * 1. Create a Notion integration at https://www.notion.so/my-integrations
 * 2. Share your "Notes" database with the integration
 * 3. Set NOTION_API_KEY environment variable with your integration token
 */

const { Client } = require('@notionhq/client');
const { marked } = require('marked');

// Get the Notion API key from environment variable (set by the wrapper script)
const NOTION_API_KEY = process.env.NOTION_API_KEY;

// Initialize Notion client
const notion = new Client({
  auth: NOTION_API_KEY,
});

/**
 * Find the "Notes" database by name
 */
async function findNotesDatabase() {
  try {
    // Search for databases with the name "Notes"
    const response = await notion.search({
      query: "Notes",
      filter: {
        property: "object",
        value: "database"
      }
    });

    // Find the database named exactly "Notes"
    const notesDatabase = response.results.find(result => {
      const titleText = result.title?.[0]?.plain_text;
      return titleText === "Notes";
    });

    if (!notesDatabase) {
      throw new Error('Could not find a database named "Notes"');
    }

    return notesDatabase.id;
  } catch (error) {
    console.error('Error finding Notes database:', error.message);
    throw error;
  }
}

/**
 * Main function
 */
async function main() {
  try {
    // Dynamically import clipboardy (ESM module)
    const clipboardyModule = await import('clipboardy');
    clipboardy = clipboardyModule.default;

    // Get today's date in ISO format (YYYY-MM-DD)
    const today = new Date().toISOString().split('T')[0];

    // Get clipboard content
    const clipboardContent = await clipboardy.read();

    if (!clipboardContent) {
      console.error('Error: Clipboard is empty');
      process.exit(1);
    }

    console.log(`Read ${clipboardContent.length} characters from clipboard`);
    console.log('Treating content as Markdown and converting to Notion blocks...');

    // Find the Notes database
    const databaseId = await findNotesDatabase();
    console.log(`Found Notes database: ${databaseId}`);

    // Find today's "Daily log" page
    const dailyLogPage = await findTodaysDailyLogPage(databaseId, today);

    if (!dailyLogPage) {
      console.error('Error: Could not find or create a "Daily log" page for today');
      process.exit(1);
    }

    console.log(`Found today's Daily log page: ${dailyLogPage.id}`);

    // Append content to the page
    await appendToPage(dailyLogPage.id, clipboardContent);

    console.log('Successfully appended content to today\'s Daily log');

  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('API response:', error.response.data);
    }
    process.exit(1);
  }
}

/**
 * Find the "Daily log" page created today
 */
async function findTodaysDailyLogPage(databaseId, today) {
  try {
    // Format today's date for the Day property
    const todayDate = new Date();
    const formattedDate = todayDate.toISOString();

    // Query the database for pages with title "Daily log" and today's date
    const response = await notion.databases.query({
      database_id: databaseId,
      filter: {
        and: [
          {
            property: 'Name',
            title: {
              equals: 'Daily log',
            },
          },
          {
            property: 'Day',
            date: {
              equals: today,
            },
          },
        ],
      },
    });

    // If we found a page, return it
    if (response.results.length > 0) {
      return response.results[0];
    }

    // If no page was found, create a new one
    return await createDailyLogPage(databaseId, today, formattedDate);

  } catch (error) {
    console.error('Error finding Daily log page:', error.message);
    throw error;
  }
}

/**
 * Create a new "Daily log" page for today
 */
async function createDailyLogPage(databaseId, _today, formattedDate) {
  try {
    const response = await notion.pages.create({
      parent: {
        database_id: databaseId,
      },
      icon: {
        type: "external",
        external: {
          url: "https://www.notion.so/icons/drafts_green.svg"
        }
      },
      properties: {
        // Title property (Name in your database)
        Name: {
          title: [
            {
              text: {
                content: 'Daily log',
              },
            },
          ],
        },
        // Type property set to "Log"
        Type: {
          select: {
            name: "Log",
          },
        },
        // Day property set to today
        Day: {
          date: {
            start: formattedDate,
          },
        },
        // Inbox property set to false
        Inbox: {
          checkbox: false,
        },
        // Archive property set to false
        Archive: {
          checkbox: false,
        },
      },
      children: [
        {
          object: 'block',
          type: 'divider',
          divider: {},
        }
      ]
    });

    return response;
  } catch (error) {
    console.error('Error creating Daily log page:', error.message);
    throw error;
  }
}

/**
 * Append content to a page
 */
async function appendToPage(pageId, content) {
  try {
    // Get the existing blocks to find where to append
    await notion.blocks.children.list({
      block_id: pageId,
    });

    // Parse the Markdown content
    const tokens = marked.lexer(content);
    console.log(`Parsed ${tokens.length} Markdown tokens`);

    // Convert Markdown tokens to Notion blocks
    const contentBlocks = convertMarkdownToNotionBlocks(tokens);

    // Create a timestamp toggle heading block
    const timestamp = new Date().toLocaleTimeString();
    const toggleHeadingBlock = {
      object: 'block',
      type: 'toggle',
      toggle: {
        rich_text: [
          {
            type: 'text',
            text: {
              content: timestamp,
            },
            annotations: {
              bold: true,
              color: 'purple'
            }
          },
        ],
        children: contentBlocks
      },
    };

    // Append the toggle heading block to the page
    await notion.blocks.children.append({
      block_id: pageId,
      children: [toggleHeadingBlock],
    });

    console.log(`Appended toggle heading with ${contentBlocks.length} content blocks to the page`);

  } catch (error) {
    console.error('Error appending to page:', error.message);
    throw error;
  }
}

/**
 * Convert Markdown tokens to Notion blocks
 */
function convertMarkdownToNotionBlocks(tokens) {
  const blocks = [];

  for (const token of tokens) {
    let block = null;

    switch (token.type) {
      case 'heading':
        // Convert headings (h1, h2, h3)
        const headingLevel = Math.min(token.depth + 1, 3); // Notion only supports h1, h2, h3
        block = {
          object: 'block',
          type: `heading_${headingLevel}`,
          [`heading_${headingLevel}`]: {
            rich_text: parseInlineFormatting(token.text),
          },
        };
        break;

      case 'paragraph':
        // Convert paragraphs
        if (token.text.trim() === '') continue;
        block = {
          object: 'block',
          type: 'paragraph',
          paragraph: {
            rich_text: parseInlineFormatting(token.text),
          },
        };
        break;

      case 'list':
        // Convert lists
        for (const item of token.items) {
          const listType = token.ordered ? 'numbered_list_item' : 'bulleted_list_item';
          blocks.push({
            object: 'block',
            type: listType,
            [listType]: {
              rich_text: parseInlineFormatting(item.text),
            },
          });
        }
        continue; // Skip adding the block since we've already added the list items

      case 'list_item':
        // Individual list items are handled in the 'list' case
        continue;

      case 'code':
        // Convert code blocks
        block = {
          object: 'block',
          type: 'code',
          code: {
            rich_text: [
              {
                type: 'text',
                text: {
                  content: token.text,
                },
              },
            ],
            language: token.lang || 'plain text',
          },
        };
        break;

      case 'blockquote':
        // Convert blockquotes
        block = {
          object: 'block',
          type: 'quote',
          quote: {
            rich_text: parseInlineFormatting(token.text),
          },
        };
        break;

      case 'hr':
        // Convert horizontal rules
        block = {
          object: 'block',
          type: 'divider',
          divider: {},
        };
        break;

      case 'space':
        // Skip space tokens
        continue;

      default:
        // For any other token types, convert to a paragraph
        if (token.text && token.text.trim() !== '') {
          block = {
            object: 'block',
            type: 'paragraph',
            paragraph: {
              rich_text: parseInlineFormatting(token.text || token.raw || ''),
            },
          };
        }
        break;
    }

    if (block) {
      blocks.push(block);
    }
  }

  return blocks;
}

/**
 * Parse inline Markdown formatting (bold, italic, code, links)
 * This is a simplified parser for common Markdown inline formatting
 */
function parseInlineFormatting(text) {
  // If text is empty or undefined, return a simple text object
  if (!text) {
    return [{ type: 'text', text: { content: '' } }];
  }

  // Simple regex patterns for basic Markdown formatting
  const boldPattern = /\*\*(.*?)\*\*/g;
  const italicPattern = /\*(.*?)\*/g;
  const codePattern = /`(.*?)`/g;
  const linkPattern = /\[(.*?)\]\((.*?)\)/g;

  // First, collect all matches and their positions
  const matches = [];

  // Find bold text
  let match;
  while ((match = boldPattern.exec(text)) !== null) {
    matches.push({
      start: match.index,
      end: match.index + match[0].length,
      content: match[1],
      format: 'bold',
      replacement: match[0]
    });
  }

  // Find italic text
  while ((match = italicPattern.exec(text)) !== null) {
    // Skip if this is actually part of a bold pattern
    if (text.substring(match.index - 1, match.index + 1) !== '**' &&
      text.substring(match.index + match[0].length - 1, match.index + match[0].length + 1) !== '**') {
      matches.push({
        start: match.index,
        end: match.index + match[0].length,
        content: match[1],
        format: 'italic',
        replacement: match[0]
      });
    }
  }

  // Find inline code
  while ((match = codePattern.exec(text)) !== null) {
    matches.push({
      start: match.index,
      end: match.index + match[0].length,
      content: match[1],
      format: 'code',
      replacement: match[0]
    });
  }

  // Find links
  while ((match = linkPattern.exec(text)) !== null) {
    matches.push({
      start: match.index,
      end: match.index + match[0].length,
      content: match[1],
      url: match[2],
      format: 'link',
      replacement: match[0]
    });
  }

  // If no formatting found, return simple text
  if (matches.length === 0) {
    return [{ type: 'text', text: { content: text } }];
  }

  // Sort matches by start position
  matches.sort((a, b) => a.start - b.start);

  // Check for overlapping matches and remove them
  for (let i = 0; i < matches.length - 1; i++) {
    if (matches[i].end > matches[i + 1].start) {
      // Remove the second match if there's an overlap
      matches.splice(i + 1, 1);
      i--; // Recheck this position
    }
  }

  // Create rich text array
  const richText = [];
  let lastEnd = 0;

  // Add text segments with formatting
  for (const match of matches) {
    // Add any text before this match
    if (match.start > lastEnd) {
      richText.push({
        type: 'text',
        text: { content: text.substring(lastEnd, match.start) }
      });
    }

    // Add the formatted text
    const textObj = {
      type: 'text',
      text: { content: match.content }
    };

    // Add appropriate formatting
    switch (match.format) {
      case 'bold':
        textObj.annotations = { bold: true };
        break;
      case 'italic':
        textObj.annotations = { italic: true };
        break;
      case 'code':
        textObj.annotations = { code: true };
        break;
      case 'link':
        textObj.text.link = { url: match.url };
        break;
    }

    richText.push(textObj);
    lastEnd = match.end;
  }

  // Add any remaining text after the last match
  if (lastEnd < text.length) {
    richText.push({
      type: 'text',
      text: { content: text.substring(lastEnd) }
    });
  }

  return richText;
}

// Run the main function
main();
