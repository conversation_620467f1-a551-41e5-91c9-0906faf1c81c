#!/usr/bin/env bash

# Main git command router script
# Usage: bin/git <command> [args...]

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Parse command line arguments
BACKEND="local" # Default backend
ARGS=()

# The command is the first argument
COMMAND="$1"
shift

# Process remaining arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
  -b | --backend)
    BACKEND="$2"
    shift 2
    ;;
  *)
    ARGS+=("$1")
    shift
    ;;
  esac
done

# Check if a command was provided
if [ -z "$COMMAND" ]; then
  echo "Usage: bin/gc <command> [args...] [-b|--backend <provider>]"
  echo "Available commands: summarize, diff-summary, create-commit-message"
  echo "Available backends: anthropic, openai, local"
  exit 1
fi

# Check if the command script exists
COMMAND_SCRIPT="$SCRIPT_DIR/git-$COMMAND.js"
if [ ! -f "$COMMAND_SCRIPT" ]; then
  echo "Unknown command: $COMMAND"
  echo "Available commands: summarize, diff-summary, create-commit-message"
  echo "Available backends: anthropic, openai, local"
  exit 1
fi

# Set up API keys based on provider
setup_provider() {
  local provider=$1

  case "$provider" in
  anthropic)
    if [ -z "$ANTHROPIC_API_KEY" ] && [ -z "$CLAUDE_API_KEY" ]; then
      # Try to get Claude API key from 1Password
      if command -v op &>/dev/null; then
        # Get the API key from 1Password
        export ANTHROPIC_API_KEY=$(op item get "Claude" --reveal --fields "API Key")
        # For backward compatibility
        export CLAUDE_API_KEY=$ANTHROPIC_API_KEY

        if [ $? -ne 0 ]; then
          echo "Error: Failed to retrieve Claude API key from 1Password"
          exit 1
        fi
      else
        echo "Error: ANTHROPIC_API_KEY environment variable is not set and 1Password CLI is not available"
        exit 1
      fi
    fi
    ;;
  openai)
    if [ -z "$OPENAI_API_KEY" ]; then
      # Try to get OpenAI API key from 1Password
      if command -v op &>/dev/null; then
        # Get the API key from 1Password
        export OPENAI_API_KEY=$(op item get "OpenAI" --reveal --fields "API Key")

        if [ $? -ne 0 ]; then
          echo "Error: Failed to retrieve OpenAI API key from 1Password"
          exit 1
        fi
      else
        echo "Error: OPENAI_API_KEY environment variable is not set and 1Password CLI is not available"
        exit 1
      fi
    fi
    ;;
  local)
    # Local LLM Studio doesn't necessarily need an API key
    # But we can set the base URL if it's not already set
    export LOCAL_LLM_BASE_URL=${LOCAL_LLM_BASE_URL:-"http://localhost:1234/v1"}
    # Set a default model name if not provided
    export LOCAL_LLM_MODEL=${LOCAL_LLM_MODEL:-"local-model"}
    ;;
  *)
    echo "Error: Unsupported LLM provider: $provider"
    echo "Supported providers: anthropic, openai, local"
    exit 1
    ;;
  esac
}

# Set the LLM provider based on the backend argument
export LLM_PROVIDER="$BACKEND"

# Set up the provider
setup_provider "$BACKEND"

# Execute the command script with the remaining arguments
echo "Using LLM backend: $BACKEND"
node "$COMMAND_SCRIPT" "${ARGS[@]}"
