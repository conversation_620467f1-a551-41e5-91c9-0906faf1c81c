#!/bin/bash

# Notion CLI Wrapper
# This script provides a command-line interface for various Notion operations
# It handles environment setup and runs the appropriate Node.js scripts

# Set up PATH to include common locations
export PATH="/opt/homebrew/bin:$HOME/.bin:$PATH"

# Change to the dotfiles directory
cd $HOME/Workspace/dotfiles

# Get the absolute path to the Node.js executable
NODE_BIN="/opt/homebrew/bin/node"

# Function to show usage information
show_usage() {
  echo "Usage: notion <command> [options]"
  echo ""
  echo "Commands:"
  echo "  append    Append clipboard content to today's Daily log"
  echo ""
  echo "Examples:"
  echo "  notion append     # Append clipboard content to Daily log"
  echo ""
}

# Check if a command was provided
if [ $# -eq 0 ]; then
  show_usage
  exit 1
fi

# Process commands
case "$1" in
append)
  # Run the append script
  $NODE_BIN bin/notion-append.js
  ;;
help)
  show_usage
  ;;
*)
  echo "Error: Unknown command '$1'"
  show_usage
  exit 1
  ;;
esac
