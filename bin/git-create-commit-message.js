#!/usr/bin/env node

/**
 * Git Commit Message Generator
 *
 * This script:
 * 1. Gets the current git status and staged changes
 * 2. Analyzes the staged changes to extract technical details
 * 3. Uses an LLM API to generate a commit message based on the changes
 * 4. Copies the message to the clipboard and outputs it to the console
 *
 * Usage: bin/gc create-commit-message [-b|--backend <provider>]
 *
 * Requirements:
 * - Node.js
 * - simple-git package (for Git operations)
 * - pbcopy (for clipboard operations on macOS)
 *
 * Setup:
 * 1. Set appropriate API key environment variable based on the LLM provider:
 *    - ANTHROPIC_API_KEY or CLAUDE_API_KEY for Anthropic Claude
 *    - OPENAI_API_KEY for OpenAI
 *    - LOCAL_LLM_API_KEY for local LLM Studio (optional)
 * 2. Use -b or --backend option to specify the LLM provider:
 *    - 'anthropic' (default)
 *    - 'openai'
 *    - 'local'
 */

const simpleGit = require('simple-git');
const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');
const llm = require('../lib/llm');

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    backend: process.env.LLM_PROVIDER || 'anthropic' // Default to anthropic if not specified
  };

  // Parse arguments
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if ((arg === '-b' || arg === '--backend') && i + 1 < args.length) {
      options.backend = args[i + 1];
      i++;
    }
  }

  return options;
}

// Get the current git repository path
function getCurrentRepoPath() {
  try {
    // Get the current working directory
    const cwd = process.cwd();

    // Check if it's a git repository
    if (!fs.existsSync(path.join(cwd, '.git'))) {
      console.error('Error: Not a git repository');
      console.error('Please run this command from within a git repository');
      process.exit(1);
    }

    return cwd;
  } catch (error) {
    console.error(`Error getting repository path: ${error.message}`);
    process.exit(1);
  }
}

// Get staged files and their diffs
async function getStagedChanges(repoPath) {
  try {
    console.log('Fetching staged changes...');

    // Initialize simple-git with the repository path
    const git = simpleGit({ baseDir: repoPath });

    // Verify that git is working in this repository
    try {
      await git.status();
    } catch (error) {
      console.error(`Error: Unable to get git status in ${repoPath}`);
      console.error(`This may not be a valid git repository or git may not be installed.`);
      process.exit(1);
    }

    // Get the status to find staged files
    const status = await git.status();

    // Check if there are any staged changes
    if (!status.staged.length) {
      console.log('No staged changes found. Please stage some changes first with "git add <file>"');
      process.exit(0);
    }

    console.log(`Found ${status.staged.length} staged files`);

    // Get the diff for staged changes
    const diff = await git.diff(['--cached']);

    return {
      stagedFiles: status.staged,
      diff: diff
    };
  } catch (error) {
    console.error(`Error getting staged changes: ${error.message}`);
    process.exit(1);
  }
}

// Generate a commit message using the configured LLM provider
async function generateCommitMessage(stagedFiles, diff, backend = 'anthropic') {
  try {
    if (!diff || diff.length === 0) {
      return 'No changes to describe.';
    }

    // Create a custom config with the specified backend
    const config = {
      provider: backend,
      ...llm.getConfigFromEnv() // Get other config values from environment
    };

    // Override the provider with the one specified
    config.provider = backend;

    const model = llm.create(config);

    console.log(`Using LLM provider: ${config.provider}`);

    // Truncate diff if it's too large (LLMs have token limits)
    const maxDiffLength = 30000; // Adjust as needed
    const truncatedDiff = diff.length > maxDiffLength
      ? diff.substring(0, maxDiffLength) + '...[truncated due to size]'
      : diff;

    const prompt = `Generate a concise, informative git commit message based on these staged changes.

    The commit message should:
    1. Start with a short (50-72 chars) summary line in imperative mood (e.g., "Add feature" not "Added feature")
    2. Leave a blank line after the summary
    3. Include a more detailed explanation if necessary
    4. Mention the key files or components changed
    5. Focus on WHY the change was made, not just WHAT was changed

    IMPORTANT: Format your response as follows:
    1. If you need to think through your reasoning, start with <think> and end with </think>
    2. Then provide ONLY the commit message, wrapped in triple backticks (\`\`\`)
    3. Do not include any other text or explanations outside the backticks

    Staged files:
    ${stagedFiles.join('\n')}

    Git diff:
    ${truncatedDiff}`;

    const response = await model.generateText({
      prompt,
      maxTokens: 800,
      temperature: 0.2
    });

    // Extract the commit message from between triple backticks
    let commitMessage = response;

    // Extract content between triple backticks if present
    // Use a non-greedy match to find content between triple backticks
    const backtickRegex = /```([\s\S]*?)```/;
    const backtickMatch = response.match(backtickRegex);

    if (backtickMatch && backtickMatch[1]) {
      commitMessage = backtickMatch[1].trim();
      console.log(`Found commit message between backticks (${commitMessage.length} chars)`);
    } else {
      console.log(`No backticks found in response, trying to extract from thinking blocks`);

      // If no backticks found, try to remove any <think>...</think> blocks
      if (commitMessage === response) {
        const thinkingRemoved = response.replace(/<think>[\s\S]*?<\/think>/g, '').trim();

        // Only use this if it actually removed something
        if (thinkingRemoved.length < response.length) {
          commitMessage = thinkingRemoved;
          console.log(`Removed thinking blocks, resulting in ${commitMessage.length} chars`);
        }
      }
    }

    // Log the extraction process for debugging
    console.log(`Final extracted commit message: ${commitMessage.length} characters`);
    if (commitMessage.includes('\n')) {
      console.log(`Message contains ${commitMessage.split('\n').length} lines`);
    }

    return {
      rawResponse: response,
      commitMessage: commitMessage
    };
  } catch (error) {
    console.error(`Error generating commit message with ${backend}:`);
    if (error.response) {
      console.error('API response:', error.response.data);
    } else {
      console.error(error.message);
    }
    process.exit(1);
  }
}

// Copy commit message to clipboard
function copyToClipboard(message) {
  try {
    // Use pbcopy on macOS to copy to clipboard
    execSync('pbcopy', { input: message });
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error.message);
    return false;
  }
}

// Main function
async function main() {
  try {
    // Parse command line arguments
    const options = parseArgs();

    // Get the current repository path
    const repoPath = getCurrentRepoPath();

    console.log(`Analyzing git repository at: ${repoPath}`);

    // Get staged changes
    const { stagedFiles, diff } = await getStagedChanges(repoPath);

    if (!diff || diff.length === 0) {
      console.log('No staged changes to analyze.');
      process.exit(0);
    }

    console.log(`Generating commit message for ${stagedFiles.length} staged files...`);
    console.log(`Using backend: ${options.backend}`);

    // Generate commit message with the specified backend
    const result = await generateCommitMessage(stagedFiles, diff, options.backend);

    if (!result || typeof result === 'string') {
      console.log('Failed to generate a proper commit message.');
      process.exit(1);
    }

    const { rawResponse, commitMessage } = result;

    // Copy only the extracted commit message to clipboard
    const copied = copyToClipboard(commitMessage);

    if (copied) {
      console.log('Commit message copied to clipboard!');
    }

    // Output to stdout
    console.log('\nGenerated Commit Message:');
    console.log('------------------------');
    console.log(commitMessage);
    console.log('------------------------');

    // If there's a difference between raw response and extracted message, show it
    if (rawResponse.trim() !== commitMessage.trim() && rawResponse.length > commitMessage.length) {
      console.log('\nNote: The LLM provided additional context that was not included in the clipboard.');
      console.log('The ENTIRE text between the dashed lines above has been copied to your clipboard.');
      console.log('\nFull LLM response:');
      console.log('------------------------');

      // Try to highlight the extracted part in the full response
      let highlightedResponse = rawResponse;
      try {
        // Escape special regex characters in the commit message
        const escapedMessage = commitMessage.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        // Create a regex that matches the commit message with word boundaries
        const messageRegex = new RegExp(`(${escapedMessage.split('\n').join('\\s*\\n\\s*')})`, 's');
        // Replace the commit message with a highlighted version
        highlightedResponse = rawResponse.replace(messageRegex, '>>>\n$1\n<<<');
      } catch (e) {
        // If regex replacement fails, just show the raw response
        console.log('(Could not highlight extracted message in response)');
      }

      console.log(highlightedResponse);
      console.log('------------------------');
    }

    console.log('\nTo use this message, run:');
    console.log('git commit -m "paste-message-here"');
    console.log('Or just run:');
    console.log('git commit');
    console.log('And paste the message in your editor (it\'s already in your clipboard)');

  } catch (error) {
    console.error('Error:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
    process.exit(1);
  }
}

// Run the main function
main();
