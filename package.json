{"name": "dotfiles", "version": "1.0.0", "description": "This setup allows you to automatically switch between different editors (VS Code and Cursor) based on the project you're working on, with smart fallbacks for different environments.", "main": "index.js", "bin": {"dotfiles": "bin/notion-append.js"}, "dependencies": {"@notionhq/client": "^2.3.0", "asynckit": "^0.4.0", "axios": "^1.9.0", "call-bind-apply-helpers": "^1.0.2", "clipboardy": "^4.0.0", "combined-stream": "^1.0.8", "delayed-stream": "^1.0.0", "dunder-proto": "^1.0.1", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "form-data": "^4.0.2", "function-bind": "^1.1.2", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2", "marked": "^15.0.11", "math-intrinsics": "^1.1.0", "mime-db": "^1.52.0", "mime-types": "^2.1.35", "node-fetch": "^2.7.0", "simple-git": "^3.27.0", "tr46": "^0.0.3", "undici-types": "^6.21.0", "webidl-conversions": "^3.0.1", "whatwg-url": "^5.0.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/solnic/dotfiles.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/solnic/dotfiles/issues"}, "homepage": "https://github.com/solnic/dotfiles#readme"}